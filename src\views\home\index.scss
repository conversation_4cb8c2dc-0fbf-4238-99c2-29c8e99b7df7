
.dashboard-container {
  :deep() {
    .tiny-scroll-text {
      position: relative;
      width: 100%;
      padding-right: 15px;
      padding-left: 40px;
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
      border: 1px solid var(--el-color-primary);
      border-radius: var(--el-border-radius-base);

      &:before {
        position: absolute;
        left: 15px;
        font-family: 'remixicon', sans-serif !important;
        content: '';
      }

      &__wrapper {
        margin-bottom: var(--el-margin);

        .up {
          div {
            text-overflow: ellipsis;
            word-break: break-all;
            white-space: nowrap;
          }
        }
      }
    }
    .dashboard-user {
      height: 168px !important;

      .el-card__body {
        display: flex;
        align-items: center;
        justify-content: center;

        .vab-icon {
          width: 100%;
          height: 130px;
        }
      }
    }
    .el-card {
      [class*='-echart'] {
        width: 100%;
        height: 170px;
      }
    }
  }
  .vab-scroll-text {
    &-primary {
      .tiny-scroll-text {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
        border: 1px solid var(--el-color-primary);
      }
    }
    &-success {
      .tiny-scroll-text {
        color: var(--el-color-success);
        background-color: var(--el-color-success-light-9);
        border: 1px solid var(--el-color-success);
      }
    }
    &-warning {
      .tiny-scroll-text {
        color: var(--el-color-warning);
        background-color: var(--el-color-warning-light-9);
        border: 1px solid var(--el-color-warning);
      }
    }
    &-danger {
      .tiny-scroll-text {
        color: var(--el-color-danger);
        background-color: var(--el-color-danger-light-9);
        border: 1px solid var(--el-color-danger);
      }
    }
    &-info {
      .tiny-scroll-text {
        color: var(--el-color-info);
        background-color: var(--el-color-info-light-9);
        border: 1px solid var(--el-color-info);
      }
    }
  }
}
