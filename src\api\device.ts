import request from '/@/utils/request'

export function getDeviceList(data: any) {
  return request({
    url: '/device/index',
    method: 'post',
    data,
  })
}

export function createDevice(data: any) {
  return request({
    url: '/device/create',
    method: 'post',
    data,
  })
}

export function getDetail(params: any) {
  return request({
    url: '/device/show',
    method: 'get',
    params,
  })
}

export function getDetailByEntity(params: any) {
  return request({
    url: '/device/showEntityId',
    method: 'get',
    params,
  })
}

//  更新设备
export const updateDevice = (data: any) => {
  return request({
    url: '/device/update',
    method: 'post',
    data,
  })
}

// 删除设备
export const deleteDevice = (data: any) => {
  return request({
    url: '/device/delete',
    method: 'post',
    data,
  })
}

// 上传文件
export const upLoadFile = (file: File) => {
  return request({
    url: '/upload/file',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'application/octet-stream',
    },
  })
}

// 批量导入
export const onImport = (data:any) => {
  return request({
    url: '/device/daoru',
    method: 'post',
    data,
  })
}
// 下载模板
export const downLoadMb = (data:any) => {
  return request({
    url: '/device/daorumb',
    method: 'post',
    data,
  })
}
