<template>
  <vab-card :body-style="{ height: '210px' }" skeleton>
    <template #header>
      <vab-icon icon="align-top" />
      设备操作排行
    </template>
    <vab-chart :option="option" />
  </vab-card>
</template>

<script setup>
import { useSettingsStore } from '/@/store/modules/settings'

const settingsStore = useSettingsStore()
const { theme } = storeToRefs(settingsStore)

const props = defineProps({
  datas: {
    type: Object,
    default: () => {},
  }
})

const option = reactive({
  tooltip: {
    trigger: 'axis',
    extraCssText: 'z-index:1',
  },
  grid: {
    top: '0%',
    left: '2%',
    right: '20%',
    bottom: '-10%',
    containLabel: true,
  },
  xAxis: [
    {
      splitLine: {
        show: false,
      },
      type: 'value',
      show: false,
    },
  ],
  yAxis: [
    {
      splitLine: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      type: 'category',
      axisTick: {
        show: false,
      },
      data: props.datas.map((item) => item.name).reverse().slice(0, 7).reverse(),
    },
  ],
  series: [
    {
      name: '开锁次数',
      type: 'bar',
      barWidth: 15,
      label: {
        show: true,
        position: 'right',
        fontSize: 12,
        formatter: ({ data }) => {
          return `${data}`
        },
      },
      itemStyle: {
        borderRadius: 10,
        borderWidth: 2,
      },
      data: props.datas.map((item) => item.action_num).reverse().slice(0, 7).reverse(),
    },
  ],
})

watch(
  theme.value,
  () => {
    option.color = [theme.value.color]
  },
  { immediate: true }
)
</script>
