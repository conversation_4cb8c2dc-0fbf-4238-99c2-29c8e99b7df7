<template>
  <div class="personnel-table-container no-background-container auto-height-container" :class="{ 'fullscreen-container': isFullscreen }">
    <el-row :gutter="20">
      <el-col :lg="3" :md="24" :sm="24" :xl="3" :xs="24">
        <vab-card class="auto-height-card">
          <el-input v-model="filterText" placeholder="请输入查询条件" style="margin-bottom: 10px" />
          <el-scrollbar max-height="600px">
            <el-tree
              ref="treeRef"
              :data="treeData"
              default-expand-all
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              :props="{ children: 'children',label: 'name' }"
              @node-click="handleNodeClick"
            />
          </el-scrollbar>
        </vab-card>
      </el-col>
      <el-col :lg="21" :md="24" :sm="24" :xl="21" :xs="24">
        <vab-card class="auto-height-card">
          <vab-query-form>
            <vab-query-form-top-panel :span="20">
              <el-form inline :model="queryForm" @submit.prevent>
                <el-form-item label="人员">
                  <el-input v-model="queryForm.phone_username" clearable placeholder="请输入人员姓名/手机号" />
                </el-form-item>
                <!-- <el-form-item label="手机号码">
                  <el-input v-model="queryForm.phone" clearable placeholder="请输入人员手机号码" />
                </el-form-item> -->
                <el-form-item label="人员类型">
                  <!-- <div style="display: flex; gap: 8px">
                    <el-check-tag
                      v-for="(label, index) in labels"
                      :key="index"
                      :checked="checkedList[index]"
                      @change="onChange(index)"
                    >
                      {{ label }}
                    </el-check-tag>
                  </div> -->

                  <el-select v-model="queryForm.user_type_id" multiple  placeholder="请选择" style="width: 300px">
                    <el-option
                      v-for="item in labels"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"/>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button :icon="Search" :loading="listLoading" native-type="submit" type="primary" @click="queryData">查询</el-button>
                </el-form-item>
                <!-- <el-form-item>
                  <el-button :icon="Plus" type="primary" @click="handleAdd">新增</el-button>
                </el-form-item> -->
              </el-form>
            </vab-query-form-top-panel>
            <vab-query-form-right-panel :span="4">
              <div class="custom-table-right-tools">
                <el-button class="hidden-xs-only">
                  <el-checkbox v-model="stripe" label="斑马纹" />
                </el-button>
                <el-button class="hidden-xs-only">
                  <el-checkbox v-model="border" label="边框" />
                </el-button>
                <el-button @click="queryData">
                  <vab-icon icon="refresh-line" />
                </el-button>
                <el-button @click="clickFullScreen">
                  <vab-icon :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'" />
                </el-button>
                <el-popover :width="165">
                  <el-radio-group v-model="lineHeight">
                    <el-radio-button label="large" value="large">大</el-radio-button>
                    <el-radio-button label="default" value="default">中</el-radio-button>
                    <el-radio-button label="small" value="small">小</el-radio-button>
                  </el-radio-group>
                  <template #reference>
                    <el-button>
                      <vab-icon icon="line-height" />
                    </el-button>
                  </template>
                </el-popover>
                <el-popover popper-class="custom-table-checkbox">
                  <template #reference>
                    <el-button>
                      <vab-icon icon="settings-line" />
                    </el-button>
                  </template>
                  <vab-draggable v-model="columns" :animation="600" target=".el-checkbox-group">
                    <el-checkbox-group v-model="checkList">
                      <el-checkbox
                        v-for="item in columns"
                        :key="item.label"
                        :disabled="item.disableCheck"
                        :label="item.label"
                        :value="item.label"
                      >
                        {{ item.label }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </vab-draggable>
                </el-popover>
              </div>
            </vab-query-form-right-panel>

            <vab-query-form-left-panel :span="20">
              <el-button :icon="Plus" type="primary" @click="handleAdd">新增</el-button>
            </vab-query-form-left-panel>
          </vab-query-form>
          <el-table
            ref="tableRef"
            v-loading="listLoading"
            :border="border"
            :data="list"
            :size="lineHeight"
            :stripe="stripe"
            @selection-change="setSelectRows"
            @sort-change="sortChange"
          >
            <el-table-column type="selection" width="38" />
            <!-- <el-table-column align="center" label="序号" width="55">
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column> -->
            <el-table-column
              v-for="(item, index) in finallyColumns"
              :key="index"
              align="center"
              :fixed="item.fixed"
              :label="item.label"
              :min-width="item.minWidth || 105"
              :prop="item.prop"
              show-overflow-tooltip
              :sortable="item.sortable"
            >
              <template #default="{ row }">
                <span v-if="item.label === '角色'">
                  {{ switchFilter(row[item.prop]) }}
                </span>
                <span v-if="item.label === '人员类型'" placement="top-start">
                  {{ row.user_type_name }}
                </span>
                <span v-if="item.label === '是否启用'">
                  <el-switch v-model="row[item.prop]" :active-value="0" :inactive-value="1" @change="PersonnelSwitch(row)"/>
                </span>
                <!-- <span v-else>{{ row[item.prop] }}</span> -->
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="220">
              <template #default="{ row }">
                <el-button text type="primary" @click="handleDetail(row)">详情</el-button>
                <el-button text type="primary" @click="handleEdit(row)">编辑</el-button>
                <!-- <el-button text type="danger" @click="handleDelete(row)">删除</el-button> -->
              </template>
            </el-table-column>
            <template #empty>
              <el-empty class="vab-data-empty" description="暂无数据" />
            </template>
          </el-table>
          <vab-pagination
            :current-page="queryForm.page"
            :page-size="queryForm.page_size"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          />
        </vab-card>
      </el-col>
    </el-row>
    <personnel-table-edit ref="editRef" :tree-data="treeData" @fetch-data="fetchData" />
  </div>
</template>

<script setup>
import { onActivated, onBeforeMount, reactive, ref, watch } from 'vue'

import PersonnelTableEdit from '/@/views/personnel/personnel_edit/index.vue'

import { useRouter } from 'vue-router'
import { VueDraggable as VabDraggable } from 'vue-draggable-plus'
import { Plus, Search, } from '@element-plus/icons-vue'
import { ElTree } from 'element-plus'
import { deletePersonnel, getPersonnelList } from '/@/api/personnel'
import { getDepartmentList, getUserTypeList } from '/@/api/system'

// 路由相关
const router = useRouter()

// 定义数据和状态
const editRef = ref(null)
const tableRef = ref(null)
const stripe = ref(false)
const border = ref(false)
const lineHeight = ref('small')
// const fold = ref(true)
const list = ref([])
const isFullscreen = ref(false)
const listLoading = ref(true)
const total = ref(0)
const selectRows = ref([])
const columns = ref([
  {
    label: '角色',
    prop: 'role_id',
    sortable: false,
    checked: true,
    minWidth: 40
  },
  {
    label: '姓名',
    prop: 'username',
    sortable: false,
    checked: true
  },
  {
    label: '手机号码',
    prop: 'phone',
    sortable: false,
    checked: true
  },
  {
    label: '所属部门',
    prop: 'dept_name',
    sortable: false,
    checked: true
  },
  {
    label: '人员类型',
    prop: 'user_type_id',
    sortable: false,
    checked: true
  },
  {
    label: '操作次数',
    prop: 'action_num',
    sortable: true,
    checked: true
  },
  {
    label: '最近一次操作时间',
    prop: 'last_action_time',
    sortable: 'custom',
    checked: true,
    // sortMethod(a, b) {
    //   const dateA = a.last_action_time ? new Date(a.last_action_time) : new Date(0);  // 如果为空则设为最早时间
    //   const dateB = b.last_action_time ? new Date(b.last_action_time) : new Date(0);
    //   return dateA - dateB;
    // }
  },
  {
    label: '是否启用',
    prop: 'is_del',
    sortable: false,
    checked: true,
    minWidth: 40
  },
])
const checkList = ref([])
const queryForm = reactive({
  page: 1,
  page_size: 20,
  dept_id: null,
  phone_username: '',
  user_type_id: [],
  time_sort: null
})

// const labels = ref(['工程', '抢修', '巡检', '装维', '检查人员']) // 金温铁路
// const labels = ref(['工程', '抢修', '巡检', '检查人员']) // 铁塔
const labels = ref([])
// const checkedList = reactive([false, false, false, false, false]) // 金温铁路
const checkedList = reactive([false, false, false, false]) // 铁塔
// const checked1 = ref(false)
// const checked2 = ref(false)
// const checked3 = ref(false)
// const checked4 = ref(false)
// const checked5 = ref(false)
// 树形数据示例
const treeData = ref([])
const filterText = ref('')
const treeRef = ref(null)

// 全屏处理
const { exit, enter, isFullscreen: _isFullscreen } = useFullscreen()

const finallyColumns = computed(() => columns.value.filter((item) => checkList.value.includes(item.label)))

// 树过滤
watch(filterText, (value) => {
  treeRef.value?.filter(value)
})

const filterNode = (value, data) => {
  if (!value) return true
  return data.label.includes(value)
}


// 获取数据
const fetchData = async () => {
  listLoading.value = true
  const response = await getPersonnelList({...queryForm, user_type_id: JSON.stringify(queryForm.user_type_id) })
  list.value = response.data.list
  total.value = response.data.total
  listLoading.value = false

  const response2 = await getUserTypeList({})
  labels.value = response2.data.list
}

const handleNodeClick = (value) => {
  if (value.parent_id === 0) {
    queryForm.dept_id = null
  } else {
    queryForm.dept_id = value.id
  }
  fetchData()
}

// 获取左侧树数据
const getTreeData = async () => {
  const { data } = await getDepartmentList()
  treeData.value = data
}

// 分页变化
const handleSizeChange = (value) => {
  queryForm.page = 1
  queryForm.page_size = value
  fetchData()
}

const handleCurrentChange = (value) => {
  queryForm.page = value
  fetchData()
}

const queryData = () => {
  queryForm.page = 1
  fetchData()
}

// 选择人员类型
const onChange = (index) => {
  checkedList[index] = !checkedList[index]
  const val = index + 1
  if (checkedList[index]) {
    queryForm.user_type_id.push(val)
  } else {
    queryForm.user_type_id = queryForm.user_type_id.filter(id => id !== val)
  }
  fetchData()
}
// const onChange = (val) => {
//   switch (val) {
//   case 1: {
//     checked1.value = !checked1.value
//     if (checked1.value) {
//       queryForm.user_type_id.push(val)
//     } else {
//       queryForm.user_type_id = queryForm.user_type_id.filter((id) => id !== val)
//     }
//   break;
//   }
//   case 2: {
//     checked2.value = !checked2.value
//     if (checked2.value) {
//       queryForm.user_type_id.push(val)
//     } else {
//       queryForm.user_type_id = queryForm.user_type_id.filter((id) => id !== val)
//       console.log(queryForm.user_type_id);
//     }
//   break;
//   }
//   case 3: {
//     checked3.value = !checked3.value
//     if (checked3.value) {
//       queryForm.user_type_id.push(val)
//     } else {
//       queryForm.user_type_id = queryForm.user_type_id.filter((id) => id !== val)
//     }
//   break;
//   }
//   case 4: {
//     checked4.value = !checked4.value
//     if (checked4.value) {
//       queryForm.user_type_id.push(val)
//     } else {
//       queryForm.user_type_id = queryForm.user_type_id.filter((id) => id !== val)
//     }
//   break;
//   }
//   case 5: {
//     checked5.value = !checked5.value
//     if (checked5.value) {
//       queryForm.user_type_id.push(val)
//     } else {
//       queryForm.user_type_id = queryForm.user_type_id.filter((id) => id !== val)
//     }
//   break;
//   }
//   // No default
//   }
//   fetchData()
// }

// 全屏切换
const clickFullScreen = () => {
  isFullscreen.value = !isFullscreen.value
  isFullscreen.value ? enter() : exit()
}

// // 状态过滤(金温铁路)
// const statusFilter = (status) => {
//   switch (status) {
//   case 1: {
//     return '工程'
//   }
//   case 2: {
//     return '抢修'
//   }
//   case 3: {
//     return '巡检'
//   }
//   case 4: {
//     return '装维'
//   }
//   case 5: {
//     return '检查人员'
//   }
//   // No default
//   }
// }

// 状态过滤(铁塔)
// const statusFilter = (status) => {
//   switch (status) {
//   case 1: {
//     return '工程'
//   }
//   case 2: {
//     return '抢修'
//   }
//   case 3: {
//     return '巡检'
//   }
//   case 5: {
//     return '检查人员'
//   }
//   // No default
//   }
// }

const statusFilter = (status) => {
  switch (status) {
  case 2: {
    return '光伏班长'
  }
  case 3: {
    return '光伏班组成员'
  }
  case 5: {
    return '风电班长'
  }
  case 6: {
    return '风电班组成员'
  }
  // No default
  }
}

// 角色过滤
const switchFilter = (status) => {
  switch (status) {
  case 1: {
    return '管理员'
  }
  case 2: {
    return '用户'
  }
  // No default
  }
}

const setSelectRows = (value) => {
  selectRows.value = value
}

// 添加
const handleAdd = () => {
  editRef.value.showEdit()
}

const handleEdit = (row = {}) => {
  editRef.value.showEdit(row)
}

// 禁用
const PersonnelSwitch = async(row) => {
  const response = await deletePersonnel({ id: row.id })
  if (response.code === 200 &&  row.is_del === 0) {
    $baseMessage('开启成功', 'success', 'hey')
  } else if (response.code === 200 && row.is_del === 1){
    $baseMessage('用户已禁用', 'success', 'hey')
  }

  await fetchData()
}

const sortChange = ({ prop, order }) => {
  if (prop === 'last_action_time') {
    if (order === 'ascending') {
      queryForm.time_sort = 0
    }
    if (order === 'descending') {
      queryForm.time_sort = 1
    }
    if (!order) {
      queryForm.time_sort = null
    }
    fetchData()
  }
}

// const handleDelete = (row) => {
//   if (row.id) {
//     $baseConfirm('您确定要删除当前项吗', null, async () => {
//       const response = await deletePersonnel({ id: row.id })
//       $baseMessage(response.msg, 'success', 'hey')
//       await fetchData()
//     })
//   } else {
//     if (selectRows.value.length > 0) {
//       const id = selectRows.value.map((item) => item.id).join(',')
//       $baseConfirm('您确定要删除选中项吗', null, async () => {
//         const response = await deletePersonnel({ id })
//         $baseMessage(response.msg, 'success', 'hey')
//         await fetchData()
//       })
//     } else {
//       $baseMessage('您未选中任何行', 'warning', 'hey')
//     }
//   }
// }

const handleDetail = (row) => {
  if (row.id) {
    router.push({
      path: '/personnel/personnelsTableDetail',
      query: {
        ...row,
        timestamp: Date.now(),
      },
    })
  } else {
    if (selectRows.value.length === 1) {
      router.push({
        path: '/personnel/personnelsTableDetail',
        query: {
          ...selectRows.value[0],
          timestamp: Date.now(),
        },
      })
    } else {
      $baseMessage('请选择一行进行详情页跳转', 'warning', 'hey')
    }
  }
}

// 监听全屏状态
watch(
  _isFullscreen,
  () => {
    isFullscreen.value = _isFullscreen.value
  },
  { immediate: true }
)

// 生命周期
onActivated(() => {
  tableRef.value?.doLayout()
})

onBeforeMount(() => {
  columns.value.forEach((item) => {
    if (item.checked) checkList.value.push(item.label)
  })
  fetchData()
  getTreeData()
})
</script>

<style lang="scss" scoped>
@import './index.scss'
</style>
