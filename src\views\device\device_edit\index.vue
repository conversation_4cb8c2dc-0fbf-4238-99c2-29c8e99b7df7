<template>
  <vab-dialog v-model="dialogFormVisible" append-to-body :title="title" width="500px" @close="close">
    <el-form ref="formRef" label-width="80px" :model="form" :rules="rules">
      <el-form-item label="设备区域" prop="c_id">
        <el-tree-select
          v-model="form.c_id"
          check-strictly
          :data="props.treeData"
          :label-key="'name'"
          :props="{ value: 'id', label: 'name'}"
          :render-after-expand="false"
          :value-key="'id'"
        >
          <template #default="{ data: { name } }">
            {{ name }}
          </template>
        </el-tree-select>
      </el-form-item>
      <el-form-item label="实体id" prop="entity_id">
        <el-input v-model.trim="form.entity_id" clearable :disabled="true" />
      </el-form-item>
      <el-form-item label="设备名称" prop="name">
        <el-input v-model.trim="form.name" clearable />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model.trim="form.remark" clearable />
      </el-form-item>
      <!-- <el-form-item label="密码" prop="password">
        <el-input v-model.trim="form.password" clearable />
      </el-form-item> -->
      <el-form-item label="安装地址" prop="addr">
        <el-input v-model.trim="form.addr" clearable />
      </el-form-item>
      <el-form-item label="状态" prop="action_type">
        <el-select v-model="form.action_type" clearable>
          <el-option v-for="item in options" :key="item.value" :disabled="item.disabled" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item :disabled="true" label="开关状态" prop="status" >
        <el-switch
         v-model="form.status"
          :active-value="2"
          :disabled="true"
          :inactive-value="1"
         />
      </el-form-item> -->
      <el-form-item label="经度" prop="lg">
        <el-input v-model.trim="form.lg" clearable />
      </el-form-item>
      <el-form-item label="纬度" prop="lt">
        <el-input v-model.trim="form.lt" clearable />
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <div style="display: flex; gap: 6px">
          <el-tag
            v-for="tag in form.tags"
            :key="tag"
            closable
            :disable-transitions="false"
            @close="handleClose(tag)"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="InputRef"
            v-model="inputValue"
            class="w-20"
            size="small"
            @blur="handleInputConfirm"
            @keyup.enter="handleInputConfirm"
          />
          <el-button v-else class="button-new-tag" size="small" @click="showInput">
            + 新标签
          </el-button>
        </div>
      </el-form-item>
      <!-- <el-form-item label="时间" prop="datetime">
        <el-date-picker v-model="form.datetime" placeholder="选择日期时间" type="datetime" />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </vab-dialog>
</template>

<script setup>
import { nextTick, reactive, ref } from 'vue'
import { createDevice, updateDevice } from '/@/api/device'

defineOptions({
  name: 'DeviceTableEdit',
})

const props = defineProps({
  treeData: {
    type: Array,
  },
})

const emit = defineEmits(['fetch-data'])

const inputValue = ref('')
const inputVisible = ref(false)
const InputRef = ref()

const formRef = ref(null)
const title = ref('')
const dialogFormVisible = ref(false)
const defaultForm  = {
  c_id: '',
  entity_id: '',
  name: '',
  remark: '',
  addr: '',
  action_type: '',
  lg: '',
  lt: '',
  tags: []
  // password: '',
  // status: '',
}
const form = reactive({...defaultForm})
const rules = reactive({
  name: [{ required: true, trigger: 'blur', message: '请输入名称' }],
  // c_id: [{ required: true, trigger: 'blur', message: '请选择设备区域' }],
  // addr: [{ required: true, trigger: 'blur', message: '请输入安装地址' }],
})
const options = ref([
  { value: 1, label: '未配置', disabled: true },
  { value: 2, label: '配置失效', disabled: true },
  { value: 3, label: '正常', disabled: false },
  { value: 4, label: '故障', disabled: false },
])

const showEdit = (row) => {
  dialogFormVisible.value = true
  nextTick(() => {
    if (row) {
      title.value = '编辑'
      if (row.tags) {
        row.tags = JSON.parse(row.tags)
      } else {
        row.tags = []
      }
      Object.assign(form, row)
    } else {
      title.value = '添加'
      for (let key in form) {
        delete form[key]
      }
      Object.assign(form, defaultForm)
    }
  })
}



const handleClose = (tag) => {
  form.tags.splice(form.tags.indexOf(tag), 1)
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value.input.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value) {
    form.tags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

defineExpose({
  showEdit,
})

const close = () => {
  formRef.value?.clearValidate()
  formRef.value?.resetFields()
  inputVisible.value = false
  inputValue.value = ''
  emit('fetch-data')
}

const save = () => {
  if (title.value === '编辑' && formRef.value) {
    formRef.value.validate(async (valid) => {
      if (valid) {
        form.tags = JSON.stringify(form.tags)
        await updateDevice(form);
        $baseMessage('编辑成功', 'success', 'hey');
        close();
        dialogFormVisible.value = false;
      }
    });
  } else if (title.value === '添加' && formRef.value) {
    formRef.value.validate(async (valid) => {
      if (valid) {
        form.tags = JSON.stringify(form.tags)
        await createDevice(form);
        $baseMessage('添加成功', 'success', 'hey');
        close();
        dialogFormVisible.value = false;
      }
    });
  }
}
</script>

