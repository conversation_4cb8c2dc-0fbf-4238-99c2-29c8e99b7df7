/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ActiveUsersBar: typeof import('./../../../src/views/index/vabAutoComponents/ActiveUsersBar.vue')['default']
    AddPanel: typeof import('./../../../src/views/other/workflow/vabAutoComponents/lFComponents/AddPanel.vue')['default']
    Authorization: typeof import('./../../../src/views/index/vabAutoComponents/Authorization.vue')['default']
    AwardGrid: typeof import('./../../../src/views/other/award/vabAutoComponents/AwardGrid.vue')['default']
    AwardSlotMachine: typeof import('./../../../src/views/other/award/vabAutoComponents/AwardSlotMachine.vue')['default']
    AwardWheel: typeof import('./../../../src/views/other/award/vabAutoComponents/AwardWheel.vue')['default']
    Branch: typeof import('./../../../src/views/index/vabAutoComponents/Branch.vue')['default']
    CarouselArrows: typeof import('./../../../src/views/vab/carousel/vabAutoComponents/CarouselArrows.vue')['default']
    CarouselBasic: typeof import('./../../../src/views/vab/carousel/vabAutoComponents/CarouselBasic.vue')['default']
    CarouselCard: typeof import('./../../../src/views/vab/carousel/vabAutoComponents/CarouselCard.vue')['default']
    CarouselIndicator: typeof import('./../../../src/views/vab/carousel/vabAutoComponents/CarouselIndicator.vue')['default']
    CarouselVertical: typeof import('./../../../src/views/vab/carousel/vabAutoComponents/CarouselVertical.vue')['default']
    CommonProperty: typeof import('./../../../src/views/other/workflow/vabAutoComponents/propertySetting/CommonProperty.vue')['default']
    Control: typeof import('./../../../src/views/other/workflow/vabAutoComponents/lFComponents/Control.vue')['default']
    DataDialog: typeof import('./../../../src/views/other/workflow/vabAutoComponents/lFComponents/DataDialog.vue')['default']
    DataScreenBottom: typeof import('./../../../src/views/index/vabAutoComponents/DataScreenBottom.vue')['default']
    DataScreenHeader: typeof import('./../../../src/views/index/vabAutoComponents/DataScreenHeader.vue')['default']
    DataScreenLeft1: typeof import('./../../../src/views/index/vabAutoComponents/DataScreenLeft1.vue')['default']
    DataScreenLeft2: typeof import('./../../../src/views/index/vabAutoComponents/DataScreenLeft2.vue')['default']
    DataScreenLeft3: typeof import('./../../../src/views/index/vabAutoComponents/DataScreenLeft3.vue')['default']
    DataScreenMap: typeof import('./../../../src/views/index/vabAutoComponents/DataScreenMap.vue')['default']
    DataScreenRight1: typeof import('./../../../src/views/index/vabAutoComponents/DataScreenRight1.vue')['default']
    DataScreenRight2: typeof import('./../../../src/views/index/vabAutoComponents/DataScreenRight2.vue')['default']
    DataScreenRight3: typeof import('./../../../src/views/index/vabAutoComponents/DataScreenRight3.vue')['default']
    DefaultTableEdit: typeof import('./../../../src/views/vab/table/vabAutoComponents/DefaultTableEdit.vue')['default']
    DepartmentManagementEdit: typeof import('./../../../src/views/setting/departmentManagement/vabAutoComponents/DepartmentManagementEdit.vue')['default']
    Develop: typeof import('./../../../src/views/index/vabAutoComponents/Develop.vue')['default']
    DictionaryManagementEdit: typeof import('./../../../src/views/setting/dictionaryManagement/vabAutoComponents/DictionaryManagementEdit.vue')['default']
    DrawerBasicUsage: typeof import('./../../../src/views/vab/drawer/vabAutoComponents/DrawerBasicUsage.vue')['default']
    DrawerCustomizationContent: typeof import('./../../../src/views/vab/drawer/vabAutoComponents/DrawerCustomizationContent.vue')['default']
    DrawerCustomizationHeader: typeof import('./../../../src/views/vab/drawer/vabAutoComponents/DrawerCustomizationHeader.vue')['default']
    DrawerNestedDrawer: typeof import('./../../../src/views/vab/drawer/vabAutoComponents/DrawerNestedDrawer.vue')['default']
    DrawerNoTitle: typeof import('./../../../src/views/vab/drawer/vabAutoComponents/DrawerNoTitle.vue')['default']
    ElAffix: typeof import('element-plus/es')['ElAffix']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAnchor: typeof import('element-plus/es')['ElAnchor']
    ElAnchorLink: typeof import('element-plus/es')['ElAnchorLink']
    ElAutoResizer: typeof import('element-plus/es')['ElAutoResizer']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBacktop: typeof import('element-plus/es')['ElBacktop']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxButton: typeof import('element-plus/es')['ElCheckboxButton']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCheckTag: typeof import('element-plus/es')['ElCheckTag']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElCountdown: typeof import('element-plus/es')['ElCountdown']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPageHeader: typeof import('element-plus/es')['ElPageHeader']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSegmented: typeof import('element-plus/es')['ElSegmented']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTableV2: typeof import('element-plus/es')['ElTableV2']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTimeSelect: typeof import('element-plus/es')['ElTimeSelect']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTour: typeof import('element-plus/es')['ElTour']
    ElTourStep: typeof import('element-plus/es')['ElTourStep']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    ErrorContainer: typeof import('./../../../src/views/error/vabAutoComponents/ErrorContainer.vue')['default']
    GoodsCard: typeof import('./../../../src/views/goods/vabAutoComponents/GoodsCard.vue')['default']
    GoodsCommentEdit: typeof import('./../../../src/views/goods/vabAutoComponents/GoodsCommentEdit.vue')['default']
    GoodsManagementEdit: typeof import('./../../../src/views/goods/vabAutoComponents/GoodsManagementEdit.vue')['default']
    IconList: typeof import('./../../../src/views/portal/vabAutoComponents/IconList.vue')['default']
    ImageBasicUsage: typeof import('./../../../src/views/vab/image/vabAutoComponents/ImageBasicUsage.vue')['default']
    ImageLazyLoad: typeof import('./../../../src/views/vab/image/vabAutoComponents/ImageLazyLoad.vue')['default']
    ImageLoadFailed: typeof import('./../../../src/views/vab/image/vabAutoComponents/ImageLoadFailed.vue')['default']
    ImagePlaceholder: typeof import('./../../../src/views/vab/image/vabAutoComponents/ImagePlaceholder.vue')['default']
    ImagePreview: typeof import('./../../../src/views/vab/image/vabAutoComponents/ImagePreview.vue')['default']
    InfiniteScrollBasic: typeof import('./../../../src/views/vab/infiniteScroll/vabAutoComponents/InfiniteScrollBasic.vue')['default']
    InfiniteScrollDisableLoading: typeof import('./../../../src/views/vab/infiniteScroll/vabAutoComponents/InfiniteScrollDisableLoading.vue')['default']
    LoginContainer: typeof import('./../../../src/views/login/vabAutoComponents/LoginContainer.vue')['default']
    MenuManagementEdit: typeof import('./../../../src/views/setting/menuManagement/vabAutoComponents/MenuManagementEdit.vue')['default']
    NodePanel: typeof import('./../../../src/views/other/workflow/vabAutoComponents/lFComponents/NodePanel.vue')['default']
    PageHeader: typeof import('./../../../src/views/index/vabAutoComponents/PageHeader.vue')['default']
    Pending: typeof import('./../../../src/views/index/vabAutoComponents/Pending.vue')['default']
    PortalDivider: typeof import('./../../../src/views/portal/vabAutoComponents/PortalDivider.vue')['default']
    PortalHeader: typeof import('./../../../src/views/portal/vabAutoComponents/PortalHeader.vue')['default']
    ProgressCircularProgressBar: typeof import('./../../../src/views/vab/progress/vabAutoComponents/ProgressCircularProgressBar.vue')['default']
    ProgressCustomColor: typeof import('./../../../src/views/vab/progress/vabAutoComponents/ProgressCustomColor.vue')['default']
    ProgressCustomizedContent: typeof import('./../../../src/views/vab/progress/vabAutoComponents/ProgressCustomizedContent.vue')['default']
    ProgressDashboardProgressBar: typeof import('./../../../src/views/vab/progress/vabAutoComponents/ProgressDashboardProgressBar.vue')['default']
    ProgressIndeterminateProgress: typeof import('./../../../src/views/vab/progress/vabAutoComponents/ProgressIndeterminateProgress.vue')['default']
    ProgressInternalPercentage: typeof import('./../../../src/views/vab/progress/vabAutoComponents/ProgressInternalPercentage.vue')['default']
    ProgressLinearProgressBar: typeof import('./../../../src/views/vab/progress/vabAutoComponents/ProgressLinearProgressBar.vue')['default']
    ProgressStripedProgress: typeof import('./../../../src/views/vab/progress/vabAutoComponents/ProgressStripedProgress.vue')['default']
    PropertyDialog: typeof import('./../../../src/views/other/workflow/vabAutoComponents/propertySetting/PropertyDialog.vue')['default']
    Rank: typeof import('./../../../src/views/index/vabAutoComponents/Rank.vue')['default']
    RankList: typeof import('./../../../src/views/index/vabAutoComponents/RankList.vue')['default']
    Recommendation: typeof import('./../../../src/views/index/vabAutoComponents/Recommendation.vue')['default']
    RoleManagementEdit: typeof import('./../../../src/views/setting/roleManagement/vabAutoComponents/RoleManagementEdit.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SegmentedBasic: typeof import('./../../../src/views/vab/segmented/vabAutoComponents/SegmentedBasic.vue')['default']
    SegmentedBlock: typeof import('./../../../src/views/vab/segmented/vabAutoComponents/SegmentedBlock.vue')['default']
    SegmentedCustomContent: typeof import('./../../../src/views/vab/segmented/vabAutoComponents/SegmentedCustomContent.vue')['default']
    SegmentedCustomStyle: typeof import('./../../../src/views/vab/segmented/vabAutoComponents/SegmentedCustomStyle.vue')['default']
    SegmentedDisabled: typeof import('./../../../src/views/vab/segmented/vabAutoComponents/SegmentedDisabled.vue')['default']
    StatisticBasic: typeof import('./../../../src/views/vab/statistic/vabAutoComponents/StatisticBasic.vue')['default']
    StatisticCard: typeof import('./../../../src/views/vab/statistic/vabAutoComponents/StatisticCard.vue')['default']
    StatisticCountdown: typeof import('./../../../src/views/vab/statistic/vabAutoComponents/StatisticCountdown.vue')['default']
    Step1: typeof import('./../../../src/views/vab/form/vabAutoComponents/Step1.vue')['default']
    Step2: typeof import('./../../../src/views/vab/form/vabAutoComponents/Step2.vue')['default']
    Step3: typeof import('./../../../src/views/vab/form/vabAutoComponents/Step3.vue')['default']
    Tabs: typeof import('./../../../src/views/index/vabAutoComponents/Tabs.vue')['default']
    TopCard: typeof import('./../../../src/views/index/vabAutoComponents/TopCard.vue')['default']
    Transactions: typeof import('./../../../src/views/goods/vabAutoComponents/Transactions.vue')['default']
    TreeAccordion: typeof import('./../../../src/views/vab/tree/vabAutoComponents/TreeAccordion.vue')['default']
    TreeBasic: typeof import('./../../../src/views/vab/tree/vabAutoComponents/TreeBasic.vue')['default']
    TreeCheckingTreeNodes: typeof import('./../../../src/views/vab/tree/vabAutoComponents/TreeCheckingTreeNodes.vue')['default']
    TreeCustomLeafNodeInLazyMode: typeof import('./../../../src/views/vab/tree/vabAutoComponents/TreeCustomLeafNodeInLazyMode.vue')['default']
    TreeCustomNodeClass: typeof import('./../../../src/views/vab/tree/vabAutoComponents/TreeCustomNodeClass.vue')['default']
    TreeCustomNodeContent: typeof import('./../../../src/views/vab/tree/vabAutoComponents/TreeCustomNodeContent.vue')['default']
    TreeDefaultExpandedAndDefaultChecked: typeof import('./../../../src/views/vab/tree/vabAutoComponents/TreeDefaultExpandedAndDefaultChecked.vue')['default']
    TreeDisabledCheckbox: typeof import('./../../../src/views/vab/tree/vabAutoComponents/TreeDisabledCheckbox.vue')['default']
    TreeDraggable: typeof import('./../../../src/views/vab/tree/vabAutoComponents/TreeDraggable.vue')['default']
    TreeNodeFiltering: typeof import('./../../../src/views/vab/tree/vabAutoComponents/TreeNodeFiltering.vue')['default']
    TreeSelectable: typeof import('./../../../src/views/vab/tree/vabAutoComponents/TreeSelectable.vue')['default']
    Trend: typeof import('./../../../src/views/index/vabAutoComponents/Trend.vue')['default']
    UploadBasic: typeof import('./../../../src/views/vab/upload/vabAutoComponents/UploadBasic.vue')['default']
    UploadCustomThumbnail: typeof import('./../../../src/views/vab/upload/vabAutoComponents/UploadCustomThumbnail.vue')['default']
    UploadDragAndDrop: typeof import('./../../../src/views/vab/upload/vabAutoComponents/UploadDragAndDrop.vue')['default']
    UploadFileList: typeof import('./../../../src/views/vab/upload/vabAutoComponents/UploadFileList.vue')['default']
    UploadFileListWithThumbnail: typeof import('./../../../src/views/vab/upload/vabAutoComponents/UploadFileListWithThumbnail.vue')['default']
    UploadLimitCover: typeof import('./../../../src/views/vab/upload/vabAutoComponents/UploadLimitCover.vue')['default']
    UploadManual: typeof import('./../../../src/views/vab/upload/vabAutoComponents/UploadManual.vue')['default']
    UploadPhotoWall: typeof import('./../../../src/views/vab/upload/vabAutoComponents/UploadPhotoWall.vue')['default']
    User: typeof import('./../../../src/views/other/workflow/vabAutoComponents/propertySetting/User.vue')['default']
    UserManagementEdit: typeof import('./../../../src/views/setting/userManagement/vabAutoComponents/UserManagementEdit.vue')['default']
    VabAlert: typeof import('./../../components/VabAlert/index.vue')['default']
    VabApp: typeof import('./../../components/VabApp/index.vue')['default']
    VabAppMain: typeof import('./../../components/VabAppMain/index.vue')['default']
    VabAvatar: typeof import('./../../components/VabAvatar/index.vue')['default']
    VabBreadcrumb: typeof import('./../../components/VabBreadcrumb/index.vue')['default']
    VabCard: typeof import('./../../components/VabCard/index.vue')['default']
    VabChart: typeof import('./../../../src/plugins/VabChart/index.vue')['default']
    VabChartBar: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartBar.vue')['default']
    VabChartCandlestick: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartCandlestick.vue')['default']
    VabChartChinaMap: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartChinaMap.vue')['default']
    VabChartFunnel: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartFunnel.vue')['default']
    VabChartGauge: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartGauge.vue')['default']
    VabChartLine: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartLine.vue')['default']
    VabChartPie: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartPie.vue')['default']
    VabChartRadar: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartRadar.vue')['default']
    VabChartScatter: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartScatter.vue')['default']
    VabChartSunburst: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartSunburst.vue')['default']
    VabChartThemeRiver: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartThemeRiver.vue')['default']
    VabChartTreemap: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartTreemap.vue')['default']
    VabChartWorldMap: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabChartWorldMap.vue')['default']
    VabCity: typeof import('./../../../src/plugins/VabCity/index.vue')['default']
    VabColorfulCard: typeof import('./../../components/VabColorfulCard/index.vue')['default']
    VabColorPicker: typeof import('./../../components/VabColorPicker/index.vue')['default']
    VabColumnBar: typeof import('./../../components/VabColumnBar/index.vue')['default']
    VabCount: typeof import('./../../../src/plugins/VabCount/index.vue')['default']
    VabDark: typeof import('./../../components/VabDark/index.vue')['default']
    VabDialog: typeof import('./../../components/VabDialog/index.vue')['default']
    VabDivider: typeof import('./../../components/VabDivider/index.vue')['default']
    VabDot: typeof import('./../../components/VabDot/index.vue')['default']
    VabErrorLog: typeof import('./../../components/VabErrorLog/index.vue')['default']
    VabErrorLogContent: typeof import('./../../components/VabErrorLog/components/VabErrorLogContent.vue')['default']
    VabFallBar: typeof import('./../../components/VabFallBar/index.vue')['default']
    VabFold: typeof import('./../../components/VabFold/index.vue')['default']
    VabFontSize: typeof import('./../../components/VabFontSize/index.vue')['default']
    VabFooter: typeof import('./../../components/VabFooter/index.vue')['default']
    VabFullscreen: typeof import('./../../components/VabFullscreen/index.vue')['default']
    VabGraphicStrokeAnimation: typeof import('./../../../src/views/other/echarts/vabAutoComponents/VabGraphicStrokeAnimation.vue')['default']
    VabHeader: typeof import('./../../components/VabHeader/index.vue')['default']
    VabIconSelector: typeof import('./../../../src/plugins/VabIconSelector/index.vue')['default']
    VabLanguage: typeof import('./../../components/VabLanguage/index.vue')['default']
    VabLink: typeof import('./../../components/VabLink/index.vue')['default']
    VabLock: typeof import('./../../components/VabLock/index.vue')['default']
    VabLogo: typeof import('./../../components/VabLogo/index.vue')['default']
    VabMagnifier: typeof import('./../../../src/plugins/VabMagnifier/index.vue')['default']
    VabMenu: typeof import('./../../components/VabMenu/index.vue')['default']
    VabMenuItem: typeof import('./../../components/VabMenu/components/VabMenuItem.vue')['default']
    VabNav: typeof import('./../../components/VabNav/index.vue')['default']
    VabNotice: typeof import('./../../components/VabNotice/index.vue')['default']
    VabPagination: typeof import('./../../components/VabPagination/index.vue')['default']
    VabPaneSplit: typeof import('./../../../src/plugins/VabPaneSplit/index.vue')['default']
    VabPlayer: typeof import('./../../../src/plugins/VabPlayer/index.vue')['default']
    VabPlayerHls: typeof import('./../../../src/plugins/VabPlayer/VabPlayerHls.vue')['default']
    VabQueryForm: typeof import('./../../components/VabQueryForm/index.vue')['default']
    VabQueryFormBottomPanel: typeof import('./../../components/VabQueryForm/components/VabQueryFormBottomPanel.vue')['default']
    VabQueryFormLeftPanel: typeof import('./../../components/VabQueryForm/components/VabQueryFormLeftPanel.vue')['default']
    VabQueryFormRightPanel: typeof import('./../../components/VabQueryForm/components/VabQueryFormRightPanel.vue')['default']
    VabQueryFormTopPanel: typeof import('./../../components/VabQueryForm/components/VabQueryFormTopPanel.vue')['default']
    VabRefresh: typeof import('./../../components/VabRefresh/index.vue')['default']
    VabRightTools: typeof import('./../../components/VabRightTools/index.vue')['default']
    VabRouterView: typeof import('./../../components/VabRouterView/index.vue')['default']
    VabSearch: typeof import('./../../components/VabSearch/index.vue')['default']
    VabSideBar: typeof import('./../../components/VabSideBar/index.vue')['default']
    VabStatistics: typeof import('./../../components/VabStatistics/index.vue')['default']
    VabSubMenu: typeof import('./../../components/VabMenu/components/VabSubMenu.vue')['default']
    VabTabs: typeof import('./../../components/VabTabs/index.vue')['default']
    VabTabsSetting: typeof import('./../../components/VabTabs/components/VabTabsSetting.vue')['default']
    VabTheme: typeof import('./../../components/VabTheme/index.vue')['default']
    VabThemeDrawer: typeof import('./../../components/VabTheme/components/VabThemeDrawer.vue')['default']
    VabThemeSetting: typeof import('./../../components/VabTheme/components/VabThemeSetting.vue')['default']
    VabUpdate: typeof import('./../../../src/plugins/VabUpdate/index.vue')['default']
    VersionInformation: typeof import('./../../../src/views/index/vabAutoComponents/VersionInformation.vue')['default']
    WorkbenchHeader: typeof import('./../../../src/views/index/vabAutoComponents/WorkbenchHeader.vue')['default']
  }
  export interface ComponentCustomProperties {
    vInfiniteScroll: typeof import('element-plus/es')['ElInfiniteScroll']
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
