<template>
  <div class="comprehensive-table-container auto-height-container">
    <vab-query-form>
      <vab-query-form-left-panel :span="24">
        <el-button :icon="Plus" type="primary" @click="handleEdit(null)">添加</el-button>
      </vab-query-form-left-panel>
    </vab-query-form>
    <el-table ref="tableRef" v-loading="listLoading" border :data="list">
      <el-table-column align="center" label="序号" width="55">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="类型" min-width="200" prop="sys_des" />
      <el-table-column align="center" :fixed="fixed" label="操作" width="215">
        <template #default="{ row }">
          <div>
            <el-button text type="primary" @click="handleEdit(row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty class="vab-data-empty" description="暂无数据" />
      </template>
    </el-table>
    <table-edit ref="editRef" @fetch-data="fetchData" />
  </div>
</template>

<script setup>
import { useSettingsStore } from '/@/store/modules/settings'
import { Plus } from '@element-plus/icons-vue';
import TableEdit from '/@/views/system/personnel_type/components/TableEdit.vue'
import { getAlarmList } from '/@/api/system'

defineOptions({
  name: 'DefaultTable',
})

const editRef = ref(null)
const tableRef = ref(null)
const list = ref([])
const listLoading = ref(true)
const foldOperation = ref(false)
const settingsStore = useSettingsStore()
const { device } = storeToRefs(settingsStore)
const fixed = ref(false)

const fetchData = async () => {
  listLoading.value = true
  const { data } = await getAlarmList()
  list.value = data
  listLoading.value = false
}
const handleEdit = (row = {}) => {
  editRef.value.showEdit(row)
}

/**
 * @description: 手机端时自动折叠并固定操作列
 * <AUTHOR>
 */
watch(
  () => device.value,
  () => {
    if (device.value === 'mobile') {
      foldOperation.value = true
      fixed.value = 'right'
    }
  },
  {
    immediate: true,
  }
)

onActivated(() => {
  tableRef.value?.doLayout()
})

onBeforeMount(() => {
  fetchData()
})
</script>
