<template>
  <div class="no-background-container">
    <div id="container"></div>
  </div>
</template>

<script setup>
import { onBeforeMount, onUnmounted } from "vue";
import { useRouter } from 'vue-router'
import { getDeviceList } from '/@/api/device'
import AMapLoader from "@amap/amap-jsapi-loader";
import lockIcon from "/@/assets/lock-icon.png"
// 路由相关
const router = useRouter()

let map = null;
const deviceList = ref([]);

// 获取设备列表数据
const fetchData = async () => {
  const response = await getDeviceList({ page: 1, page_size: 9999 });
  deviceList.value = response.data.list;
};

// 创建地图
const creatMap = async () => {
  globalThis._AMapSecurityConfig = {
    securityJsCode: "d2784c829aa0d8be28e86f5cabd25dbf",
  };
  await AMapLoader.load({
    key: "8e8bba48c05db7d06c5d0dd427df3004", // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ["AMap.Geolocation", 'AMap.convertFrom'], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
  })
    .then((AMap) => {
      map = new AMap.Map("container", {
        viewMode: "3D", // 是否为3D地图模式
        zoom: 15, // 初始化地图级别
      });
      // 添加定位插件
      const geolocation = new AMap.Geolocation({
        enableHighAccuracy: true, // 是否使用高精度定位
        timeout: 10000, // 定位超时时间，单位毫秒
        position: "RB", // 定位按钮的位置
      });

      // 添加定位控件到地图
      map.addControl(geolocation);

      // 获取定位并设置中心点
      geolocation.getCurrentPosition((status, result) => {
        if (status === "complete") {
          const { lng, lat } = result.position;
          map.setCenter([lng, lat]); // 设置地图中心点
          console.log(`当前位置：经度 ${lng}, 纬度 ${lat}`);
        } else {
          console.error("定位失败:", result.message);
        }
      });
    })
    .catch((error) => {
      console.log(error);
    });
};
// 使用高德地图 API 进行坐标转换（WGS-84 到 GCJ-02）
const convertToGCJ02 = (lng, lat) => {
  return new Promise((resolve, reject) => {
    AMap.convertFrom([lng, lat], "gps", (status, result) => {
      if (status === "complete" && result.info === "ok") {
        const gcj02 = result.locations[0]; // 获取转换后的坐标
        resolve(gcj02); // 返回转换后的坐标
      } else {
        reject(new Error("坐标转换失败"));
      }
    });
  });
};
// 添加点位
const addMarkers = async () => {
  if (!map || deviceList.value.length === 0) return;

  // 使用 map 函数遍历设备列表，进行异步坐标转换
  const markerPromises = deviceList.value.map(async (device) => {
    try {
      if (!device.lg || !device.lt || isNaN(device.lg) || isNaN(device.lt)) {
        return
      }

      // 确保经纬度是数值类型
      const lng = parseFloat(device.lg);  // 转换为浮动数字
      const lat = parseFloat(device.lt);  // 转换为浮动数字

      // 验证经纬度范围
      if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
        return;
      }

      // 使用异步方式转换坐标
      const gcj02 = await convertToGCJ02(lng, lat);

      // 创建 Marker
      const marker = new AMap.Marker({
        position: [gcj02.lng, gcj02.lat], // 使用转换后的经纬度
        title: device.name || "设备位置", // 可选：标记的标题
        icon: new AMap.Icon({
          size: new AMap.Size(30, 30), // 图标的大小
          image: lockIcon, // 自定义图标的路径
          imageSize: new AMap.Size(30, 30), // 图标图片的显示尺寸
          anchor: new AMap.Pixel(15, 15), // 锚点设置为图标的中心
        }),
      })

      // 将 Marker 添加到地图
      marker.setMap(map)

      // 为 Marker 添加点击事件
      marker.on("click", () => {
        router.push({
          path: '/device/devicesTableDetail',
          query: {
            ...device,
            timestamp: Date.now(),
          },
        })
      })
    } catch (error) {
      console.error("坐标转换失败或标记添加失败:", device, error)
    }
  });

  // 等待所有的异步操作完成
  await Promise.all(markerPromises)
};

// 确保按顺序执行
const initialize = async () => {
  await fetchData(); // 获取设备数据
  await creatMap(); // 创建地图
  addMarkers(); // 添加 Marker
};

onBeforeMount(() => {
  initialize();
});

onUnmounted(() => {
  map?.destroy();
});
</script>

<style scoped>
#container {
  width: 100%;
  height: 75vh;
}
</style>
