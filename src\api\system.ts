import request from '/@/utils/request'

export function getAreaList(params: any) {
  return request({
    url: '/devicecategory/index',
    method: 'post',
    params,
  })
}

export function createArea(data: any) {
  return request({
    url: '/devicecategory/create',
    method: 'post',
    data,
  })
}

export function updateArea(data: any) {
  return request({
    url: '/devicecategory/update',
    method: 'post',
    data,
  })
}

export function deleteArea(data: any) {
  return request({
    url: '/devicecategory/delete',
    method: 'post',
    data,
  })
}

export function getDepartmentList(params: any) {
  return request({
    url: '/department/index',
    method: 'post',
    params,
  })
}

export function createDepartment(data: any) {
  return request({
    url: '/department/create',
    method: 'post',
    data,
  })
}

export function updateDepartment(data: any) {
  return request({
    url: '/department/update',
    method: 'post',
    data,
  })
}

export function deleteDepartment(data: any) {
  return request({
    url: '/department/delete',
    method: 'post',
    data,
  })
}

export function getUserTypeList(params: any) {
  return request({
    url: '/SySPost/index',
    method: 'post',
    params,
  })
}

export function createUserType(data: any) {
  return request({
    url: '/SySPost/create',
    method: 'post',
    data,
  })
}

export function updateUserType(data: any) {
  return request({
    url: '/SySPost/update',
    method: 'post',
    data,
  })
}

export function deleteUserType(data: any) {
  return request({
    url: '/SysPost/delete',
    method: 'post',
    data,
  })
}

export function getAlarmList(params: any) {
  return request({
    url: '/syssetting/list',
    method: 'get',
    params,
  })
}

export function updateAlarm(params: any) {
  return request({
    url: '/syssetting/update',
    method: 'get',
    params,
  })
}

