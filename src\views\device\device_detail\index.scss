.device-table-detail-container {
  :deep() {
    .el-timeline-item__dot {
      [class*='ri'] {
        width: 12px;
        height: 12px;
        margin-left: -3px;
        background: var(--el-color-white);
      }

      .vab-dot {
        left: -1px;
        width: 12px;
        height: 12px;
        margin: auto !important;
      }
    }
  }

  .user-info {
    padding: var(--el-padding);
    text-align: center;

    :deep() {
      .el-avatar {
        img {
          padding: var(--el-padding);
          cursor: pointer;
        }
      }

      .btn-box {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }

    &-full-name {
      margin-top: 15px;
      font-size: 24px;
      font-weight: 500;
      color: var(--el-color-grey);
    }

    &-description {
      margin-top: 8px;
    }

    &-follow {
      margin-top: 15px;
    }

    &-list {
      margin-top: 18px;
      line-height: 30px;
      text-align: left;
      list-style: none;

      h5 {
        margin: -20px 0 5px;
      }
    }
  }

  .item {
    display: flex;

    i {
      font-size: 40px;
    }

    &-content {
      box-sizing: border-box;
      flex: 1;
      margin-left: var(--el-margin);

      &-second {
        margin-top: 8px;
      }
    }
  }
  .auto-height-card {
    height: 100%;
  }
  .el-timeline {
    padding: 20px 10px;
    height: 460px;
    overflow-y: auto;
  }
  ::-webkit-scrollbar {
    display: none;
  }
}
