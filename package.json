{"name": "shop-vite", "version": "10.2.5", "private": true, "author": "github.com/zxwk1998", "scripts": {"dev": "cross-env VITE_CJS_IGNORE_WARNING=true vite", "dev:vue-tsc": "vue-tsc --noEmit && cross-env VITE_CJS_IGNORE_WARNING=true vite", "dev:https": "cross-env VITE_CJS_IGNORE_WARNING=true vite --config ./vite.config.dev.ts", "vue-tsc": "vue-tsc --noEmit", "build": "vue-tsc --noEmit && cross-env VITE_CJS_IGNORE_WARNING=true vite build", "build:fast": "cross-env VITE_CJS_IGNORE_WARNING=true NODE_OPTIONS=--max-old-space-size=8192 vite build", "build:report": "cross-env VITE_CJS_IGNORE_WARNING=true NODE_OPTIONS=--max-old-space-size=8192 vite build --config ./vite.config.dev.ts", "build:website": "cross-env VITE_CJS_IGNORE_WARNING=true NODE_OPTIONS=--max-old-space-size=8192 vite build --config ./vite.config.website.ts && node write.version.js", "build:vue-tsc": "vue-tsc --noEmit && cross-env VITE_CJS_IGNORE_WARNING=true vite build", "preview": "cross-env VITE_CJS_IGNORE_WARNING=true vite preview", "lint:eslint": "eslint . --fix", "lint:prettier": "prettier {src,mock,library,types}/**/*.{html,vue,css,sass,scss,js,ts} --write", "lint:stylelint": "stylelint {src,mock,library}/**/*.{vue,css,sass,scss} --fix --cache --cache-location node_modules/.cache/stylelint/", "global:install": "npm i -g nrm cnpm npm-check-updates pnpm", "globle:update": "ncu -g", "module:install": "pnpm i", "module:update": "ncu -u --reject vite-plugin-mock,eslint,vue-echarts,chokidar,sass,@opentiny/vue && npm run module:install", "module:reinstall": "rimraf node_modules && npm run module:install", "git": "start ./git.sh", "template": "plop"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@babylonjs/core": "^8.31.0", "@babylonjs/loaders": "^8.31.0", "@element-plus/icons-vue": "^2.3.1", "@kangc/v-md-editor": "^2.3.18", "@logicflow/core": "^2.0.8", "@logicflow/extension": "^2.0.12", "@lucky-canvas/vue": "^0.1.11", "@opentiny/vue": "3.18.0", "@vueuse/core": "^12.0.0", "@vueuse/head": "^2.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.7.8", "dayjs": "^1.11.13", "disable-devtool": "^0.3.8", "echarts": "^5.5.1", "element-plus": "^2.8.8", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "mockjs": "^1.1.0", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pinia": "^2.2.6", "qs": "^6.13.1", "sortablejs": "^1.15.4", "typeit": "^8.8.7", "viewerjs": "^1.11.7", "vsv-context-menu": "^1.4.2", "vsv-icon": "^1.2.2", "vue": "^3.5.13", "vue-draggable-plus": "^0.6.0", "vue-echarts": "6.7.3", "vue-i18n": "^10.0.4", "vue-json-viewer": "^3.0.4", "vue-pdf-embed": "^2.1.1", "vue-qr": "^4.0.9", "vue-router": "^4.5.0", "vue3-gantt": "1.1.8-7", "vue3-puzzle-vcode": "^1.1.7", "xgplayer": "^3.0.20", "xgplayer-hls.js": "^3.0.20"}, "devDependencies": {"@element-plus/eslint-config": "^2.8.5", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.17", "@vitejs/plugin-basic-ssl": "^1.2.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/compiler-sfc": "^3.5.13", "adm-zip": "^0.5.16", "autoprefixer": "^10.4.20", "chokidar": "^3.6.0", "cross-env": "^7.0.3", "eslint": "^8.18.0", "eslint-define-config": "^2.1.0", "eslint-plugin-unicorn": "^56.0.1", "lint-staged": "^15.2.10", "picocolors": "^1.1.1", "plop": "^4.0.1", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "prettier": "^3.4.1", "rollup-plugin-visualizer": "^5.12.0", "sass": "1.79.5", "stylelint": "^16.10.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "terser": "^5.36.0", "typescript": "^5.7.2", "unplugin-auto-import": "^0.18.5", "unplugin-vue-components": "^0.27.4", "vite": "^6.0.1", "vite-plugin-banner": "^0.8.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-mock": "2.9.8", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-unplugin": "^1.8.0", "vite-plugin-vitebar": "^0.0.8", "vue-tsc": "^2.1.10"}, "gitHooks": {"pre-commit": "lint-staged"}, "homepage": "https://vuejs-core.cn/shop-vite", "license": "Mozilla Public License Version 2.0", "lint-staged": {"*.{js,ts,vue}": ["npm run lint", "npm run lint:prettier", "git add"]}, "participants": ["FlowPeakFish"], "repository": {"type": "git", "url": "git+https://github.com/zxwk2024/shop-vite.git"}}