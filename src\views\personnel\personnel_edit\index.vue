<template>
  <vab-dialog v-model="dialogFormVisible" append-to-body :title="title" width="500px" @close="close">
    <el-form ref="formRef" label-width="80px" :model="form" :rules="rules">
      <el-form-item label="用户名" prop="username">
        <el-input v-model.trim="form.username" clearable />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model.trim="form.password" clearable />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model.trim="form.phone" clearable />
      </el-form-item>
      <el-form-item label="所属部门" prop="dept_id">
        <el-tree-select
          v-model="form.dept_id"
          check-strictly
          :data="props.treeData"
          :label-key="'name'"
          :props="{ value: 'id', label: 'name'}"
          :render-after-expand="false"
          :value-key="'id'"
        >
          <template #default="{ data: { name } }">
            {{ name }}
          </template>
        </el-tree-select>
      </el-form-item>
      <el-form-item label="角色" prop="role_id">
        <el-select v-model="form.role_id" clearable>
          <el-option label="管理员" :value="1" />
          <el-option label="用户" :value="2" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="人员类型" prop="user_type_id">
        <el-select v-model="form.user_type_id" clearable>
          <el-option label="工程" :value="1" />
          <el-option label="抢修" :value="2" />
          <el-option label="巡检" :value="3" />
          <el-option label="装维" :value="4" />
          <el-option label="检查人员" :value="5" />
        </el-select>
      </el-form-item> -->

      <el-form-item label="人员类型" prop="user_type_id">
        <el-select v-model="form.user_type_id" clearable>
          <el-option
            v-for="item in labels"
            :key="item.id"
            :label="item.name"
            :value="item.id"/>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </vab-dialog>
</template>

<script setup>
import { nextTick, reactive, ref } from 'vue'
import { getUserTypeList } from '~/src/api/system'
import { createPersonnel, updatePersonnel } from '/@/api/personnel'

defineOptions({
  name: 'DeviceTableEdit',
})

const props = defineProps({
  treeData: {
    type: Array,
  },
})

const emit = defineEmits(['fetch-data'])
const formRef = ref(null)
const title = ref('')
const dialogFormVisible = ref(false)
const defaultForm  = {
  username: '',
  password: '',
  phone: '',
  dept_id: '',
  role_id: 2,
  user_type_id: '',
}
const labels = ref([]);
const form = reactive({...defaultForm})
const rules = reactive({
  username: [{ required: true, trigger: 'blur', message: '请输入姓名' }],
  password: [{ required: true, trigger: 'blur', message: '请输入密码' }],
  phone: [{ required: true, trigger: 'blur', message: '请输入手机号' }],
  // dept_id: [{ required: true, trigger: 'blur', message: '请选择所属部门' }],
  // role_id: [{ required: true, trigger: 'blur', message: '请选择角色' }],
  // user_type_id: [{ required: true, trigger: 'blur', message: '请选择人员类型' }],
})

const fetchData = async () => {
  const response = await getUserTypeList({});
  labels.value = response.data.list;
}

const showEdit = (row) => {
  dialogFormVisible.value = true;
  fetchData();
  nextTick(() => {
    if (row) {
      title.value = '编辑'
      Object.assign(form, row)
    } else {
      title.value = '添加'
      for (let key in form) {
        delete form[key]
      }
      Object.assign(form, defaultForm)
    }
  })
}

defineExpose({
  showEdit,
})

const close = () => {
  formRef.value?.clearValidate()
  formRef.value?.resetFields()
  emit('fetch-data')
}

const save = () => {
  if (title.value === '编辑' && formRef.value) {
    formRef.value.validate(async (valid) => {
      if (valid) {
        delete form['dept_name']
        delete form['user_type_name']
        await updatePersonnel(form);
        $baseMessage('编辑成功', 'success', 'hey');
        close();
        dialogFormVisible.value = false;
      }
    });
  } else if (title.value === '添加' && formRef.value) {
    formRef.value.validate(async (valid) => {
      if (valid) {
        await createPersonnel(form);
        $baseMessage('添加成功', 'success', 'hey');
        close();
        dialogFormVisible.value = false;
      }
    });
  }
}
</script>

