<template>
  <vab-dialog v-model="dialogFormVisible" append-to-body :title="title" width="500px" @close="close">
    <el-form ref="formRef" :model="form" :rules="rules">
      <el-form-item label="类型名称" prop="sys_des">
        <el-input v-model.trim="form.sys_des" clearable />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </vab-dialog>
</template>

<script setup>
import { updateAlarm } from '/@/api/system'

defineOptions({
  name: 'DefaultTableEdit',
})

const emit = defineEmits(['fetch-data'])

const formRef = ref()
const title = ref('')
const dialogFormVisible = ref(false)
const form = ref({
  sys_des: '',
})
const rules = reactive({
  sys_des: [{ required: true, trigger: 'blur', message: '请输入类型' }],
})

const showEdit = (row) => {
  dialogFormVisible.value = true
  nextTick(() => {
    if (row) {
      title.value = '编辑'
      Object.assign(form.value, row)
    } else {
      title.value = '新增'
    }
  })
}

defineExpose({
  showEdit,
})

const close = () => {
  formRef.value?.clearValidate()
  formRef.value?.resetFields()
  form.value = {
    sys_des: '',
  }
  emit('fetch-data')
}

const save = async() => {
  console.log(form.value)
  // if (sys_des.value === form.sys_des && fz.value === form.fz) {
  //   await $baseMessage('没有进行修改', 'warning', 'hey')
  //   close()
  //   dialogFormVisible.value = false
  //   return
  // }
  // formRef.value?.validate(async (valid) => {
  //   if (valid) {
  //     await updateAlarm(form)
  //     await $baseMessage('修改成功', 'success', 'hey')
  //     await close()
  //     dialogFormVisible.value = false
  //   }
  // })
}
</script>
