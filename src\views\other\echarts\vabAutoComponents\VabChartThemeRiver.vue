<template>
  <el-col :lg="8" :md="12" :sm="24" :xl="6" :xs="24">
    <vab-card :body-style="{ height: '240px' }" skeleton :title="title">
      <vab-chart :option="option" />
    </vab-card>
  </el-col>
</template>

<script lang="ts" setup>
import { random } from 'lodash-es'
import { useSettingsStore } from '/@/store/modules/settings'

defineOptions({
  name: 'VabChartThemeRiver',
})

defineProps({
  title: {
    type: String,
    default: '',
  },
})

const settingsStore = useSettingsStore()
const { theme } = storeToRefs(settingsStore)
let timer: ReturnType<typeof setInterval>

const option = reactive<any>({
  grid: {
    top: 20,
    right: 20,
    bottom: 40,
    left: 40,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: 'rgba(0,0,0,0.2)',
        width: 1,
        type: 'solid',
      },
    },
  },
  singleAxis: {
    top: 20,
    bottom: 20,
    type: 'time',
  },

  series: {
    type: 'themeRiver',
    data: [
      ['2015/11/08', random(0, 100), 'DQ'],
      ['2015/11/09', random(0, 100), 'DQ'],
      ['2015/11/10', random(0, 100), 'DQ'],
      ['2015/11/11', random(0, 100), 'DQ'],
      ['2015/11/12', random(0, 100), 'DQ'],
      ['2015/11/13', random(0, 100), 'DQ'],
      ['2015/11/14', random(0, 100), 'DQ'],
      ['2015/11/15', random(0, 100), 'DQ'],
      ['2015/11/16', random(0, 100), 'DQ'],
      ['2015/11/17', random(0, 100), 'DQ'],
      ['2015/11/18', random(0, 100), 'DQ'],
      ['2015/11/19', random(0, 100), 'DQ'],
      ['2015/11/20', random(0, 100), 'DQ'],
      ['2015/11/21', random(0, 100), 'DQ'],
      ['2015/11/22', random(0, 100), 'DQ'],
      ['2015/11/23', random(0, 100), 'DQ'],
      ['2015/11/24', random(0, 100), 'DQ'],
      ['2015/11/25', random(0, 100), 'DQ'],
      ['2015/11/26', random(0, 100), 'DQ'],
      ['2015/11/27', random(0, 100), 'DQ'],
      ['2015/11/28', random(0, 100), 'DQ'],
      ['2015/11/08', random(0, 100), 'TY'],
      ['2015/11/09', random(0, 100), 'TY'],
      ['2015/11/10', random(0, 100), 'TY'],
      ['2015/11/11', random(0, 100), 'TY'],
      ['2015/11/12', random(0, 100), 'TY'],
      ['2015/11/13', random(0, 100), 'TY'],
      ['2015/11/14', random(0, 100), 'TY'],
      ['2015/11/15', random(0, 100), 'TY'],
      ['2015/11/16', random(0, 100), 'TY'],
      ['2015/11/17', random(0, 100), 'TY'],
      ['2015/11/18', random(0, 100), 'TY'],
      ['2015/11/19', random(0, 100), 'TY'],
      ['2015/11/20', random(0, 100), 'TY'],
      ['2015/11/21', random(0, 100), 'TY'],
      ['2015/11/22', random(0, 100), 'TY'],
      ['2015/11/23', random(0, 100), 'TY'],
      ['2015/11/24', random(0, 100), 'TY'],
      ['2015/11/25', random(0, 100), 'TY'],
      ['2015/11/26', random(0, 100), 'TY'],
      ['2015/11/27', random(0, 100), 'TY'],
      ['2015/11/28', random(0, 100), 'TY'],
      ['2015/11/08', random(0, 100), 'SS'],
      ['2015/11/09', random(0, 100), 'SS'],
      ['2015/11/10', random(0, 100), 'SS'],
      ['2015/11/11', random(0, 100), 'SS'],
      ['2015/11/12', random(0, 100), 'SS'],
      ['2015/11/13', random(0, 100), 'SS'],
      ['2015/11/14', random(0, 100), 'SS'],
      ['2015/11/15', random(0, 100), 'SS'],
      ['2015/11/16', random(0, 100), 'SS'],
      ['2015/11/17', random(0, 100), 'SS'],
      ['2015/11/18', random(0, 100), 'SS'],
      ['2015/11/19', random(0, 100), 'SS'],
      ['2015/11/20', random(0, 100), 'SS'],
      ['2015/11/21', random(0, 100), 'SS'],
      ['2015/11/22', random(0, 100), 'SS'],
      ['2015/11/23', random(0, 100), 'SS'],
      ['2015/11/24', random(0, 100), 'SS'],
      ['2015/11/25', random(0, 100), 'SS'],
      ['2015/11/26', random(0, 100), 'SS'],
      ['2015/11/27', random(0, 100), 'SS'],
      ['2015/11/28', random(0, 100), 'SS'],
      ['2015/11/08', random(0, 100), 'QG'],
      ['2015/11/09', random(0, 100), 'QG'],
      ['2015/11/10', random(0, 100), 'QG'],
      ['2015/11/11', random(0, 100), 'QG'],
      ['2015/11/12', random(0, 100), 'QG'],
      ['2015/11/13', random(0, 100), 'QG'],
      ['2015/11/14', random(0, 100), 'QG'],
      ['2015/11/15', random(0, 100), 'QG'],
      ['2015/11/16', random(0, 100), 'QG'],
      ['2015/11/17', random(0, 100), 'QG'],
      ['2015/11/18', random(0, 100), 'QG'],
      ['2015/11/19', random(0, 100), 'QG'],
      ['2015/11/20', random(0, 100), 'QG'],
      ['2015/11/21', random(0, 100), 'QG'],
      ['2015/11/22', random(0, 100), 'QG'],
      ['2015/11/23', random(0, 100), 'QG'],
      ['2015/11/24', random(0, 100), 'QG'],
      ['2015/11/25', random(0, 100), 'QG'],
      ['2015/11/26', random(0, 100), 'QG'],
      ['2015/11/27', random(0, 100), 'QG'],
      ['2015/11/28', random(0, 100), 'QG'],
      ['2015/11/08', random(0, 100), 'SY'],
      ['2015/11/09', random(0, 100), 'SY'],
      ['2015/11/10', random(0, 100), 'SY'],
      ['2015/11/11', random(0, 100), 'SY'],
      ['2015/11/12', random(0, 100), 'SY'],
      ['2015/11/13', random(0, 100), 'SY'],
      ['2015/11/14', random(0, 100), 'SY'],
      ['2015/11/15', random(0, 100), 'SY'],
      ['2015/11/16', random(0, 100), 'SY'],
      ['2015/11/17', random(0, 100), 'SY'],
      ['2015/11/18', random(0, 100), 'SY'],
      ['2015/11/19', random(0, 100), 'SY'],
      ['2015/11/20', random(0, 100), 'SY'],
      ['2015/11/21', random(0, 100), 'SY'],
      ['2015/11/22', random(0, 100), 'SY'],
      ['2015/11/23', random(0, 100), 'SY'],
      ['2015/11/24', random(0, 100), 'SY'],
      ['2015/11/25', random(0, 100), 'SY'],
      ['2015/11/26', random(0, 100), 'SY'],
      ['2015/11/27', random(0, 100), 'SY'],
      ['2015/11/28', random(0, 100), 'SY'],
      ['2015/11/08', random(0, 100), 'DD'],
      ['2015/11/09', random(0, 100), 'DD'],
      ['2015/11/10', random(0, 100), 'DD'],
      ['2015/11/11', random(0, 100), 'DD'],
      ['2015/11/12', random(0, 100), 'DD'],
      ['2015/11/13', random(0, 100), 'DD'],
      ['2015/11/14', random(0, 100), 'DD'],
      ['2015/11/15', random(0, 100), 'DD'],
      ['2015/11/16', random(0, 100), 'DD'],
      ['2015/11/17', random(0, 100), 'DD'],
      ['2015/11/18', random(0, 100), 'DD'],
      ['2015/11/19', random(0, 100), 'DD'],
      ['2015/11/20', random(0, 100), 'DD'],
      ['2015/11/21', random(0, 100), 'DD'],
      ['2015/11/22', random(0, 100), 'DD'],
      ['2015/11/23', random(0, 100), 'DD'],
      ['2015/11/24', random(0, 100), 'DD'],
      ['2015/11/25', random(0, 100), 'DD'],
      ['2015/11/26', random(0, 100), 'DD'],
      ['2015/11/27', random(0, 100), 'DD'],
      ['2015/11/28', random(0, 100), 'DD'],
    ],
  },
})

watch(
  theme.value,
  () => {
    option.color = [theme.value.color]
  },
  { immediate: true }
)

onMounted(() => {
  timer = setInterval(() => {
    option.series.data = [
      ['2015/11/08', random(0, 100), 'DQ'],
      ['2015/11/09', random(0, 100), 'DQ'],
      ['2015/11/10', random(0, 100), 'DQ'],
      ['2015/11/11', random(0, 100), 'DQ'],
      ['2015/11/12', random(0, 100), 'DQ'],
      ['2015/11/13', random(0, 100), 'DQ'],
      ['2015/11/14', random(0, 100), 'DQ'],
      ['2015/11/15', random(0, 100), 'DQ'],
      ['2015/11/16', random(0, 100), 'DQ'],
      ['2015/11/17', random(0, 100), 'DQ'],
      ['2015/11/18', random(0, 100), 'DQ'],
      ['2015/11/19', random(0, 100), 'DQ'],
      ['2015/11/20', random(0, 100), 'DQ'],
      ['2015/11/21', random(0, 100), 'DQ'],
      ['2015/11/22', random(0, 100), 'DQ'],
      ['2015/11/23', random(0, 100), 'DQ'],
      ['2015/11/24', random(0, 100), 'DQ'],
      ['2015/11/25', random(0, 100), 'DQ'],
      ['2015/11/26', random(0, 100), 'DQ'],
      ['2015/11/27', random(0, 100), 'DQ'],
      ['2015/11/28', random(0, 100), 'DQ'],
      ['2015/11/08', random(0, 100), 'TY'],
      ['2015/11/09', random(0, 100), 'TY'],
      ['2015/11/10', random(0, 100), 'TY'],
      ['2015/11/11', random(0, 100), 'TY'],
      ['2015/11/12', random(0, 100), 'TY'],
      ['2015/11/13', random(0, 100), 'TY'],
      ['2015/11/14', random(0, 100), 'TY'],
      ['2015/11/15', random(0, 100), 'TY'],
      ['2015/11/16', random(0, 100), 'TY'],
      ['2015/11/17', random(0, 100), 'TY'],
      ['2015/11/18', random(0, 100), 'TY'],
      ['2015/11/19', random(0, 100), 'TY'],
      ['2015/11/20', random(0, 100), 'TY'],
      ['2015/11/21', random(0, 100), 'TY'],
      ['2015/11/22', random(0, 100), 'TY'],
      ['2015/11/23', random(0, 100), 'TY'],
      ['2015/11/24', random(0, 100), 'TY'],
      ['2015/11/25', random(0, 100), 'TY'],
      ['2015/11/26', random(0, 100), 'TY'],
      ['2015/11/27', random(0, 100), 'TY'],
      ['2015/11/28', random(0, 100), 'TY'],
      ['2015/11/08', random(0, 100), 'SS'],
      ['2015/11/09', random(0, 100), 'SS'],
      ['2015/11/10', random(0, 100), 'SS'],
      ['2015/11/11', random(0, 100), 'SS'],
      ['2015/11/12', random(0, 100), 'SS'],
      ['2015/11/13', random(0, 100), 'SS'],
      ['2015/11/14', random(0, 100), 'SS'],
      ['2015/11/15', random(0, 100), 'SS'],
      ['2015/11/16', random(0, 100), 'SS'],
      ['2015/11/17', random(0, 100), 'SS'],
      ['2015/11/18', random(0, 100), 'SS'],
      ['2015/11/19', random(0, 100), 'SS'],
      ['2015/11/20', random(0, 100), 'SS'],
      ['2015/11/21', random(0, 100), 'SS'],
      ['2015/11/22', random(0, 100), 'SS'],
      ['2015/11/23', random(0, 100), 'SS'],
      ['2015/11/24', random(0, 100), 'SS'],
      ['2015/11/25', random(0, 100), 'SS'],
      ['2015/11/26', random(0, 100), 'SS'],
      ['2015/11/27', random(0, 100), 'SS'],
      ['2015/11/28', random(0, 100), 'SS'],
      ['2015/11/08', random(0, 100), 'QG'],
      ['2015/11/09', random(0, 100), 'QG'],
      ['2015/11/10', random(0, 100), 'QG'],
      ['2015/11/11', random(0, 100), 'QG'],
      ['2015/11/12', random(0, 100), 'QG'],
      ['2015/11/13', random(0, 100), 'QG'],
      ['2015/11/14', random(0, 100), 'QG'],
      ['2015/11/15', random(0, 100), 'QG'],
      ['2015/11/16', random(0, 100), 'QG'],
      ['2015/11/17', random(0, 100), 'QG'],
      ['2015/11/18', random(0, 100), 'QG'],
      ['2015/11/19', random(0, 100), 'QG'],
      ['2015/11/20', random(0, 100), 'QG'],
      ['2015/11/21', random(0, 100), 'QG'],
      ['2015/11/22', random(0, 100), 'QG'],
      ['2015/11/23', random(0, 100), 'QG'],
      ['2015/11/24', random(0, 100), 'QG'],
      ['2015/11/25', random(0, 100), 'QG'],
      ['2015/11/26', random(0, 100), 'QG'],
      ['2015/11/27', random(0, 100), 'QG'],
      ['2015/11/28', random(0, 100), 'QG'],
      ['2015/11/08', random(0, 100), 'SY'],
      ['2015/11/09', random(0, 100), 'SY'],
      ['2015/11/10', random(0, 100), 'SY'],
      ['2015/11/11', random(0, 100), 'SY'],
      ['2015/11/12', random(0, 100), 'SY'],
      ['2015/11/13', random(0, 100), 'SY'],
      ['2015/11/14', random(0, 100), 'SY'],
      ['2015/11/15', random(0, 100), 'SY'],
      ['2015/11/16', random(0, 100), 'SY'],
      ['2015/11/17', random(0, 100), 'SY'],
      ['2015/11/18', random(0, 100), 'SY'],
      ['2015/11/19', random(0, 100), 'SY'],
      ['2015/11/20', random(0, 100), 'SY'],
      ['2015/11/21', random(0, 100), 'SY'],
      ['2015/11/22', random(0, 100), 'SY'],
      ['2015/11/23', random(0, 100), 'SY'],
      ['2015/11/24', random(0, 100), 'SY'],
      ['2015/11/25', random(0, 100), 'SY'],
      ['2015/11/26', random(0, 100), 'SY'],
      ['2015/11/27', random(0, 100), 'SY'],
      ['2015/11/28', random(0, 100), 'SY'],
      ['2015/11/08', random(0, 100), 'DD'],
      ['2015/11/09', random(0, 100), 'DD'],
      ['2015/11/10', random(0, 100), 'DD'],
      ['2015/11/11', random(0, 100), 'DD'],
      ['2015/11/12', random(0, 100), 'DD'],
      ['2015/11/13', random(0, 100), 'DD'],
      ['2015/11/14', random(0, 100), 'DD'],
      ['2015/11/15', random(0, 100), 'DD'],
      ['2015/11/16', random(0, 100), 'DD'],
      ['2015/11/17', random(0, 100), 'DD'],
      ['2015/11/18', random(0, 100), 'DD'],
      ['2015/11/19', random(0, 100), 'DD'],
      ['2015/11/20', random(0, 100), 'DD'],
      ['2015/11/21', random(0, 100), 'DD'],
      ['2015/11/22', random(0, 100), 'DD'],
      ['2015/11/23', random(0, 100), 'DD'],
      ['2015/11/24', random(0, 100), 'DD'],
      ['2015/11/25', random(0, 100), 'DD'],
      ['2015/11/26', random(0, 100), 'DD'],
      ['2015/11/27', random(0, 100), 'DD'],
      ['2015/11/28', random(0, 100), 'DD'],
    ]
  }, 3000)
})

onBeforeUnmount(() => {
  if (timer) clearTimeout(timer)
})
</script>
storeToRefslettimer
