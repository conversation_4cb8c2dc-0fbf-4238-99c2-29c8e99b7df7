<template>
  <div class="alarm-tabs  auto-height-container">
    <el-tabs v-model="activeName" @tab-change="onChangeTabs">
      <el-tab-pane label="超时未关锁" name="1">
        <vab-query-form>
          <vab-query-form-left-panel :span="24">
            <el-form inline :model="queryForm" @submit.prevent>
              <el-form-item label="开始时间">
                <el-date-picker
                  v-model="queryForm.start_time"
                  format="YYYY/MM/DD"
                  placeholder="请选择开始时间"
                  type="date"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item  label="结束时间">
                <el-date-picker
                  v-model="queryForm.end_time"
                  format="YYYY/MM/DD"
                  placeholder="请选择结束时间"
                  type="date"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item>
                <el-button :icon="Search" type="primary" @click="queryData">查询</el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-left-panel>
        </vab-query-form>
        <el-table :data="tableData">
          <!-- <el-table-column align="center" label="设备ID"  min-width="60" prop="device_id" /> -->
          <el-table-column align="center" label="设备名称" min-width="50" prop="device_name" />
          <el-table-column align="center" label="区域" min-width="60" prop="province" >
            <template #default="{ row }">
                {{ row.device_c_name }}
              </template>
          </el-table-column>
          <el-table-column align="center" label="安装位置" min-width="80" prop="device_addr" />
          <el-table-column align="center" label="设备状态" min-width="80">
              <template #default="{ row }">
                <span v-if="row.device_status === '1'" style="color: #F56C6C;">关</span>
                <span v-if="row.device_status === '2'" style="color: #67C23A;">开</span>
              </template>
          </el-table-column>
          <el-table-column align="center" label="持续时间" prop="title" >
            <template #default="{ row }">
              {{ row.title }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="最近一次操作时间" prop="alarm_time" />
          <el-table-column align="center" label="最近一次操作人员" prop="username" />
          <template #empty>
            <el-empty class="vab-data-empty" description="暂无数据" />
          </template>
        </el-table>
        <vab-pagination
          :current-page="queryForm.page"
          :page-size="queryForm.page_size"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </el-tab-pane>
      <el-tab-pane label="长期未操作" name="2">
        <vab-query-form>
          <vab-query-form-left-panel :span="24">
            <el-form inline :model="queryForm" @submit.prevent>
              <el-form-item label="开始时间">
                <el-date-picker
                  v-model="queryForm.start_time"
                  format="YYYY/MM/DD"
                  placeholder="请选择开始时间"
                  type="date"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item  label="结束时间">
                <el-date-picker
                  v-model="queryForm.end_time"
                  format="YYYY/MM/DD"
                  placeholder="请选择结束时间"
                  type="date"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item>
                <el-button :icon="Search" type="primary" @click="queryData">查询</el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-left-panel>
        </vab-query-form>
        <el-table :data="tableData">
          <!-- <el-table-column align="center" label="设备ID" min-width="60" prop="device_id" /> -->
          <el-table-column align="center" label="设备名称" min-width="50" prop="device_name" />
          <el-table-column align="center" label="区域" min-width="60" prop="province" >
            <template #default="{ row }">
                {{ row.device_c_name }}
              </template>
          </el-table-column>
          <el-table-column align="center" label="安装位置" min-width="80" prop="device_addr" />
          <el-table-column align="center" label="设备状态" min-width="80">
              <template #default="{ row }">
                <span v-if="row.device_status === '1'" style="color: #F56C6C;">关</span>
                <span v-if="row.device_status === '2'" style="color: #67C23A;">开</span>
              </template>
          </el-table-column>
          <el-table-column align="center" label="持续时间" prop="title" >
            <template #default="{ row }">
              {{ row.title }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="最近一次操作时间" prop="alarm_time" />
          <el-table-column align="center" label="最近一次操作人员" prop="username" />
          <template #empty>
            <el-empty class="vab-data-empty" description="暂无数据" />
          </template>
        </el-table>
        <vab-pagination
          :current-page="queryForm.page"
          :page-size="queryForm.page_size"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </el-tab-pane>
      <el-tab-pane label="设备位置偏移" name="3">
        <vab-query-form>
          <vab-query-form-left-panel :span="24">
            <el-form inline :model="queryForm" @submit.prevent>
              <el-form-item label="开始时间">
                <el-date-picker
                  v-model="queryForm.start_time"
                  format="YYYY/MM/DD"
                  placeholder="请选择开始时间"
                  type="date"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item  label="结束时间">
                <el-date-picker
                  v-model="queryForm.end_time"
                  format="YYYY/MM/DD"
                  placeholder="请选择结束时间"
                  type="date"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item>
                <el-button :icon="Search" type="primary" @click="queryData">查询</el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-left-panel>
        </vab-query-form>
        <el-table :data="tableData">
          <!-- <el-table-column align="center" label="设备ID" min-width="60" prop="device_id" /> -->
          <el-table-column align="center" label="设备名称" min-width="50" prop="device_name" />
          <el-table-column align="center" label="区域" min-width="60" prop="province" >
            <template #default="{ row }">
                {{ row.device_c_name }}
              </template>
          </el-table-column>
          <el-table-column align="center" label="安装位置" min-width="80" prop="device_addr" />
          <el-table-column align="center" label="设备状态" min-width="80">
              <template #default="{ row }">
                <span v-if="row.device_status === '1'" style="color: #F56C6C;">关</span>
                <span v-if="row.device_status === '2'" style="color: #67C23A;">开</span>
              </template>
          </el-table-column>
          <el-table-column align="center" label="最近一次操作时间" prop="alarm_time" />
          <el-table-column align="center" label="最近一次操作人员" prop="username" />
          <el-table-column align="center" label="偏移量" min-width="120" prop="title">
            <template #default="{ row }">
              {{ row.title }}
            </template>
          </el-table-column>
          <template #empty>
            <el-empty class="vab-data-empty" description="暂无数据" />
          </template>
        </el-table>
        <vab-pagination
          :current-page="queryForm.page"
          :page-size="queryForm.page_size"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { Search, } from '@element-plus/icons-vue'
import { getHomeList } from '/@/api/home'
import { getAreaList } from '/@/api/system'
const activeName = ref('1')
const treeData = ref()

const total = ref(0)
const queryForm = reactive({
  page: 1,
  page_size: 10,
  alarm_type: 1,
  start_time: '',
  end_time: ''
})

const tableData = ref([])

const getTableList = async() => {
  const { data } = await getHomeList(queryForm)
  tableData.value = data.list
  total.value = data.total
}

const getTreeData = async () => {
  const { data } = await getAreaList()
  treeData.value = data
}

// 分页变化
const handleSizeChange = (value) => {
  queryForm.page = 1
  queryForm.page_size = value
  getTableList()
}

const handleCurrentChange = (value) => {
  queryForm.page = value
  getTableList()
}

// 标签页切换
const onChangeTabs = (value) => {
  queryForm.page = 1
  queryForm.start_time = ''
  queryForm.end_time = ''
  queryForm.alarm_type = value
  getTableList()
}

const queryData = () => {
  queryForm.page = 1
  getTableList()
}
onBeforeMount(() => {
  getTreeData()
  getTableList()
})
</script>

<style lang="scss" scoped>
.item {
  padding: 0 12px 0 0;
}
</style>
