<template>
  <div class="list-container auto-height-container" :class="{ 'fullscreen-container': isFullscreen }">
    <vab-query-form>
      <vab-query-form-left-panel :span="16">
        <el-form inline :model="queryForm" @submit.prevent>
          <el-form-item label="开始时间">
            <el-date-picker
              v-model="queryForm.start_time"
              format="YYYY/MM/DD"
              placeholder="请选择开始时间"
              type="date"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item  label="结束时间">
            <el-date-picker
              v-model="queryForm.end_time"
              format="YYYY/MM/DD"
              placeholder="请选择结束时间"
              type="date"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button :icon="Search" :loading="listLoading" type="primary" @click="queryData">查询</el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="8">
        <div class="custom-table-right-tools">
          <el-button class="hidden-xs-only">
            <el-checkbox v-model="stripe" label="斑马纹" />
          </el-button>
          <el-button class="hidden-xs-only">
            <el-checkbox v-model="border" label="边框" />
          </el-button>
          <el-button @click="queryData">
            <vab-icon icon="refresh-line" />
          </el-button>
          <el-button @click="clickFullScreen">
            <vab-icon :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'" />
          </el-button>
          <el-popover :width="165">
            <el-radio-group v-model="lineHeight">
              <el-radio-button label="large" value="large">大</el-radio-button>
              <el-radio-button label="default" value="default">中</el-radio-button>
              <el-radio-button label="small" value="small">小</el-radio-button>
            </el-radio-group>
            <template #reference>
              <el-button>
                <vab-icon icon="line-height" />
              </el-button>
            </template>
          </el-popover>
          <el-popover popper-class="custom-table-checkbox">
            <template #reference>
              <el-button>
                <vab-icon icon="settings-line" />
              </el-button>
            </template>
            <vab-draggable v-model="columns" :animation="600" target=".el-checkbox-group">
              <el-checkbox-group v-model="checkList">
                <el-checkbox
                  v-for="item in columns"
                  :key="item.label"
                  :disabled="item.disableCheck"
                  :label="item.label"
                  :value="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </el-checkbox-group>
            </vab-draggable>
          </el-popover>
        </div>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableRef"
      v-loading="listLoading"
      :border="border"
      :data="list"
      :size="lineHeight"
      :stripe="stripe"
    >
      <el-table-column align="center" label="序号" width="55">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :fixed="item.fixed"
        :label="item.label"
        :min-width="item.minWidth || 105"
        :prop="item.prop"
        show-overflow-tooltip
        :sortable="item.sortable"
        >
        <template #default="{ row }">
          <span v-if="item.label === '内容'">
            <span style="color: #409EFF;">{{ row[item.prop].split('/')[0].split('对')[0] }}</span>对<span style="color: #409EFF;">{{ row[item.prop].split('/')[0].split('对')[1] }}</span>/<span style="color: #409EFF;">{{ row[item.prop].split('/')[1] }}</span>/<span :style="row[item.prop].split('/')[2].includes('取消') ? 'color: #F56C6C;' : 'color: #67C23A;'">{{ row[item.prop].split('/')[2] }}</span>/<span style="color: #E6A23C;">{{ row[item.prop].split('/')[3] }}</span>
          </span>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty class="vab-data-empty" description="暂无数据" />
      </template>
    </el-table>
    <vab-pagination
      :current-page="queryForm.page"
      :page-size="queryForm.page_size"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'
import { VueDraggable as VabDraggable } from 'vue-draggable-plus'
import { getAuthorizeLog } from '/@/api/log'

defineOptions({
  name: 'List',
})

const tableRef = ref(null)
const stripe = ref(false)
const border = ref(false)
const lineHeight = ref('small')
const isFullscreen = ref(false)
// 全屏处理
const { exit, enter, isFullscreen: _isFullscreen } = useFullscreen()

const columns = ref([
  // {
  //   label: '设备ID',
  //   prop: 'device_id',
  //   sortable: false,
  //   checked: true,
  // },
  {
    label: '设备名称',
    prop: 'device_name',
    sortable: false,
    checked: true,
  },
  {
    label: '授权时间',
    prop: 'created_at',
    sortable: false,
    checked: true,
  },
  {
    label: '内容',
    prop: 'content',
    sortable: false,
    checked: true,
    minWidth: 300
  }
])
const checkList = ref([])
const list = ref([])
const total = ref(0)
const queryForm = reactive({
  page: 1,
  page_size: 20,
  start_time: '',
  end_time: ''
})

const listLoading = ref(true)
const emptyShow = ref(true)

const finallyColumns = computed(() => columns.value.filter((item) => checkList.value.includes(item.label)))

const fetchData = async () => {
  listLoading.value = true
  const { data } = await getAuthorizeLog(queryForm)
  list.value = data.list
  total.value = data.total
  listLoading.value = false
  emptyShow.value = data.total <= 0
}

const handleSizeChange = (value) => {
  queryForm.page = 1
  queryForm.page_size = value
  fetchData()
}

const handleCurrentChange = (value) => {
  queryForm.page = value
  fetchData()
}

const queryData = () => {
  queryForm.page = 1
  fetchData()
}

// 全屏切换
const clickFullScreen = () => {
  isFullscreen.value = !isFullscreen.value
  isFullscreen.value ? enter() : exit()
}

// 监听全屏状态
watch(
  _isFullscreen,
  () => {
    isFullscreen.value = _isFullscreen.value
  },
  { immediate: true }
)

onBeforeMount(() => {
  columns.value.forEach((item) => {
    if (item.checked) checkList.value.push(item.label)
  })
  fetchData()
})
</script>

<style lang="scss" scoped>
@import './index.scss'
</style>
