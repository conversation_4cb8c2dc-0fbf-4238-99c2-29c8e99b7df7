<svg viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M300 105.361c0 31.559-25.584 57.142-57.143 57.142s-57.143-25.583-57.143-57.142c0-31.56 25.584-57.143 57.143-57.143S300 73.802 300 105.36Z" fill="#2F9BFF"/><g filter="url(#a)"><path fill-rule="evenodd" clip-rule="evenodd" d="M68.75 64.647c0-7.496 6.076-13.572 13.571-13.572h33.929c.133 0 .266.002.398.006 6.485-.181 12.386 3.96 13.979 10.519l8.066 32.636h159.968c6.337 0 12.349 3.518 16.372 8.424 4.019 4.902 5.629 12.028 4.383 18.247l-14.606 73.692c-1.97 9.898-10.663 17.694-20.76 17.694H152.638c-10.096 0-18.79-7.839-20.764-17.737L105.248 78.218H82.321c-7.495 0-13.571-6.076-13.571-13.571Zm194.361 199.712c10.694 0 19.362-8.669 19.362-19.362 0-10.693-8.668-19.361-19.362-19.361-10.693 0-19.361 8.668-19.361 19.361 0 10.693 8.668 19.362 19.361 19.362Zm-88.037.001c10.693 0 19.362-8.668 19.362-19.361 0-10.693-8.669-19.362-19.362-19.362-10.693 0-19.361 8.669-19.361 19.362 0 10.693 8.668 19.361 19.361 19.361Z" fill="#88BAFF" fill-opacity=".3"/></g><circle cx="169.643" cy="38.393" r="19.643" fill="#2F9BFF"/><g filter="url(#b)"><g filter="url(#c)"><path d="M192.099 157.325a7.408 7.408 0 0 1 10.407-1.197l18.947 15.04a7.408 7.408 0 0 1-9.21 11.604l-18.948-15.04a7.407 7.407 0 0 1-1.196-10.407Z" fill="#fff"/></g><g filter="url(#d)"><path d="M241.397 157.325a7.408 7.408 0 0 0-10.407-1.197l-18.947 15.04a7.408 7.408 0 0 0 9.21 11.604l18.948-15.04a7.407 7.407 0 0 0 1.196-10.407Z" fill="#fff"/></g><g filter="url(#e)"><rect x="224.536" y="125" width="59.259" height="14.815" rx="7.407" transform="rotate(90 224.536 125)" fill="#fff"/></g></g><defs><filter id="a" x="34.821" y="17.147" width="318.936" height="281.142" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feGaussianBlur in="BackgroundImage" stdDeviation="16.964"/><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_35_2170"/><feBlend in="SourceGraphic" in2="effect1_backgroundBlur_35_2170" result="shape"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dx="5.429" dy="13.571"/><feGaussianBlur stdDeviation="8.951"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/><feBlend in2="shape" result="effect2_innerShadow_35_2170"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="3.393"/><feGaussianBlur stdDeviation="3.393"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"/><feBlend in2="effect2_innerShadow_35_2170" result="effect3_innerShadow_35_2170"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="-3.393"/><feGaussianBlur stdDeviation="3.393"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix values="0 0 0 0 0.103594 0 0 0 0 0.52793 0 0 0 0 0.920833 0 0 0 0.16 0"/><feBlend in2="effect3_innerShadow_35_2170" result="effect4_innerShadow_35_2170"/></filter><filter id="b" x="180.346" y="114.853" width="72.805" height="82.025" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feGaussianBlur in="BackgroundImage" stdDeviation="5.074"/><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_35_2170"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="6.25"/><feColorMatrix values="0 0 0 0 0.184314 0 0 0 0 0.607843 0 0 0 0 1 0 0 0 0.3 0"/><feBlend in2="effect1_backgroundBlur_35_2170" result="effect2_dropShadow_35_2170"/><feBlend in="SourceGraphic" in2="effect2_dropShadow_35_2170" result="shape"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dx="5" dy="12.5"/><feGaussianBlur stdDeviation="8.245"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/><feBlend in2="shape" result="effect3_innerShadow_35_2170"/></filter><filter id="c" x="180.346" y="144.375" width="54.057" height="52.502" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feGaussianBlur in="BackgroundImage" stdDeviation="5.074"/><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_35_2170"/><feBlend in="SourceGraphic" in2="effect1_backgroundBlur_35_2170" result="shape"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dx="5" dy="12.5"/><feGaussianBlur stdDeviation="8.245"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/><feBlend in2="shape" result="effect2_innerShadow_35_2170"/></filter><filter id="d" x="199.093" y="144.375" width="54.057" height="52.502" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feGaussianBlur in="BackgroundImage" stdDeviation="5.074"/><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_35_2170"/><feBlend in="SourceGraphic" in2="effect1_backgroundBlur_35_2170" result="shape"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dx="5" dy="12.5"/><feGaussianBlur stdDeviation="8.245"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/><feBlend in2="shape" result="effect2_innerShadow_35_2170"/></filter><filter id="e" x="199.574" y="114.853" width="35.109" height="81.907" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feGaussianBlur in="BackgroundImage" stdDeviation="5.074"/><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_35_2170"/><feBlend in="SourceGraphic" in2="effect1_backgroundBlur_35_2170" result="shape"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dx="5" dy="12.5"/><feGaussianBlur stdDeviation="8.245"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/><feBlend in2="shape" result="effect2_innerShadow_35_2170"/></filter></defs></svg>