<template>
  <vab-dialog v-model="dialogFormVisible" append-to-body :title="title" width="500px" @close="close">
    <el-form ref="formRef" label-width="80px" :model="form" :rules="rules">
      <el-form-item label="父节点" prop="parent_id">
        <el-tree-select
          v-model="form.parent_id"
          check-strictly
          :data="treeData"
          :label-key="'name'"
          :props="{ value: 'id', label: 'name'}"
          :render-after-expand="false"
          :value-key="'id'"
        >
          <template #default="{ data: { name } }">
            {{ name }}
          </template>
        </el-tree-select>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" clearable />
      </el-form-item>
      <!-- <el-form-item label="排序" prop="order">
        <el-input v-model="form.order" clearable />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </vab-dialog>
</template>

<script setup>
import { nextTick, onBeforeMount, reactive, ref } from 'vue';
import { createArea, getAreaList, updateArea } from '/@/api/system';

defineOptions({
  name: 'DepartmentManagementEdit',
});

const emit = defineEmits(['fetch-data']);

const formRef = ref(null);
const treeData = ref([]);
const form = reactive({
  parent_id: '',
});
const rules = reactive({
  // parent_id: [{ required: true, trigger: 'blur', message: '请选择父节点' }],
  name: [{ required: true, trigger: 'blur', message: '请输入名称' }],
  // order: [{ required: true, trigger: 'blur', message: '请输入排序' }],
});
const title = ref('');
const dialogFormVisible = ref(false);

const fetchData = async () => {
  const { data } = await getAreaList();
  treeData.value = data;
};

// 根据 id 删除节点的递归方法
const deleteNodeById = (id) => {
  const deleteNode = (nodes) => {
    return nodes.filter(node => {
      if (node.id === id) {
        return false; // 找到目标节点，返回 false 即可删除该节点
      }
      if (node.children) {
        node.children = deleteNode(node.children);
      }
      return true; // 保留当前节点
    });
  };
  treeData.value = deleteNode(treeData.value);
}

const showEdit = (row) => {
  dialogFormVisible.value = true;
  nextTick(() => {
    if (row) {
      delete row.value; // 删除 `value` 属性，避免冲突
      title.value = '编辑';
      deleteNodeById(row.id);
      Object.assign(form, row);
    } else {
      title.value = '添加';
      for (let key in form) {
        delete form[key]
      }
      Object.assign(form, {
        parent_id: ''
      })
    }
  });
};

defineExpose({
  showEdit,
});

const close = () => {
  console.log(formRef.value);
  if (formRef.value) {
    formRef.value.clearValidate();
    formRef.value.resetFields();
  }
  fetchData()
  emit('fetch-data');
};

const save = () => {
  if (title.value === '编辑' && formRef.value) {
    formRef.value.validate(async (valid) => {
      if (valid) {
        form.parent_id ||= 0;
        await updateArea(form);
        $baseMessage('编辑成功', 'success', 'hey');
        close();
        dialogFormVisible.value = false;
      }
    });
  } else if (title.value === '添加' && formRef.value) {
    formRef.value.validate(async (valid) => {
      if (valid) {
        form.parent_id ||= 0;
        await createArea(form);
        $baseMessage('添加成功', 'success', 'hey');
        close();
        dialogFormVisible.value = false;
      }
    });
  }
};

onBeforeMount(() => {
  fetchData()
});
</script>


<style lang="scss" scoped>
:deep() {
  .el-select {
    width: 100%;
  }
}
</style>
