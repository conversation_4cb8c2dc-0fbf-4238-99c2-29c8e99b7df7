<template>
  <el-scrollbar wrap-class="scroll-wrap-portal">
    <div class="portal-main">
      <portal-header active-menu="portal" />
      <div class="carousel-background" :style="{ background: background }"></div>
      <main class="hidden-xs-only" style="padding-top: 85px">
        <el-row :gutter="0">
          <!-- <el-col :span="6">
            <div class="left-tab">
              <el-menu active-text-color="var(--el-color-white)" background-color="#39364d" text-color="var(--el-color-white)">
                <el-menu-item index="1" @click="openWindow('https://vuejs-core.cn/admin-pro')">
                  <template #title>Vue Admin Pro：企业级中后台前端框架</template>
                </el-menu-item>
                <el-menu-item index="2" @click="openWindow('https://vuejs-core.cn/admin-plus')">
                  <template #title>Vue Admin Plus：企业级中后台前端框架</template>
                </el-menu-item>
                <el-menu-item index="3" @click="openWindow('https://vuejs-core.cn/shop-vite')">
                  <template #title>Smart Lock System：全新一代前端模板</template>
                </el-menu-item>
              </el-menu>
            </div>
          </el-col> -->
          <el-col :span="24">
            <el-carousel arrow="always" height="600px" :interval="3000" @change="handleChange">
              <el-carousel-item />
              <el-carousel-item />
              <el-carousel-item />
            </el-carousel>
          </el-col>
          <el-col :span="24">
            <div style="background-color: #f5f7fa">
              <div class="description-box">
                <el-row>
                  <el-col :span="6">
                    <div class="show-box">
                      <div style="float: left">
                        <h1>
                          <span class="clip">Forti钜安</span>
                        </h1>
                        <p class="text">全新一代的智能安全锁</p>
                      </div>
                    </div>
                    <vab-divider direction="vertical" />
                  </el-col>
                  <el-col :span="6">
                    <div class="show-box">
                      <div class="system-class-icon" style="background: #20c2dc">
                        <vab-icon icon="bubble-chart-line" />
                      </div>
                      <div class="describe">智能 Intelligence</div>
                    </div>
                    <vab-divider direction="vertical" />
                  </el-col>
                  <el-col :span="6">
                    <div class="show-box">
                      <div class="system-class-icon" style="background: #f7753f">
                        <vab-icon icon="medal-fill" />
                      </div>
                      <div class="describe">安全 Safe</div>
                    </div>
                    <vab-divider direction="vertical" />
                  </el-col>
                  <el-col :span="6">
                    <div class="show-box">
                      <div class="system-class-icon" style="background: #6a59f4">
                        <vab-icon icon="seedling-fill" />
                      </div>
                      <div class="describe">耐用 Durable</div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-col>
        </el-row>
      </main>
      <main>
        <el-carousel arrow="always" class="hidden-sm-and-up" height="200px" :interval="3000" style="margin-top: 70px">
          <el-carousel-item @click="openWindow('https://vuejs-core.cn/admin-pro')" />
          <el-carousel-item @click="openWindow('https://vuejs-core.cn/admin-plus')" />
          <el-carousel-item @click="openWindow('https://vuejs-core.cn/shop-vite')" />
        </el-carousel>
        <el-row :gutter="20">
          <el-col :lg="10" :md="10" :sm="24" :xl="10" :xs="24">
            <div class="news-tit"><h2>产品演示</h2></div>
            <!-- <el-image class="news-img" :src="banner_1" />
            <el-image class="news-img" :src="banner_1" /> -->
            <!-- <video controls width="100%">
              <source :src="video_2" type="video/mp4" />
            </video>
            <video controls width="100%">
              <source :src="video_1" type="video/mp4" />
            </video> -->
          </el-col>
          <el-col :lg="14" :md="14" :sm="24" :xl="14" :xs="24">
            <div class="news-tit"><h2>相关信息</h2></div>
            <el-row :gutter="24">
              <el-col v-for="item in list" :key="item.title" :lg="12" :md="12" :sm="24" :xl="12" :xs="24">
                <div class="solution-box">
                  <div class="solution-box-item">
                    <div class="solution-box-title">{{ item.title }}</div>
                    <div v-if="item.img">
                      <el-image
                        fit="cover"
                        :preview-src-list="[code]"
                        :preview-teleported="true"
                        :src="code"
                        style="width: 50px; height: 50px;"
                      />
                      <el-image
                        fit="cover"
                        :preview-src-list="[code]"
                        :preview-teleported="true"
                        :src="code"
                        style="width: 50px; height: 50px;"
                      />
                    </div>
                    <div v-else>
                      <div v-for="(text ,index) in item.description" :key="index" class="solution-box-description">{{ text }}</div>
                    </div>
                  </div>
                  <vab-icon :icon="item.icon" is-custom-svg />
                </div>
              </el-col>
            </el-row>
          </el-col>
          <!-- <el-col :lg="8" :md="8" :sm="24" :xl="8" :xs="24">
            <div class="news-tit"><h2>工作日程</h2></div>
            <el-calendar v-model="date" style="border: 1px solid var(--el-border-color)" />
          </el-col>
          <el-col :lg="16" :md="16" :sm="24" :xl="16" :xs="24">
            <div class="news-tit"><h2>互动留言</h2></div>
            <el-table :data="tableData" :height="395" style="border: 1px solid var(--el-border-color)">
              <el-table-column label="Date" prop="date" />
              <el-table-column label="Name" prop="name" />
              <el-table-column label="Address" prop="address" show-overflow-tooltip />
            </el-table>
          </el-col> -->
        </el-row>
        <!-- <portal-divider active-menu="portal" style="margin-top: 12px" /> -->
      </main>

      <vab-footer />
    </div>
    <el-backtop target="#app .scroll-wrap-portal" />
    <vab-theme-setting />
  </el-scrollbar>
</template>

<script setup>
// import banner_1 from '/@/assets/portal_images/banner_1.jpg'
import carousel_1 from '/@/assets/portal_images/carousel_1.jpg'
import carousel_2 from '/@/assets/portal_images/carousel_2.jpg'
import carousel_3 from '/@/assets/portal_images/carousel_3.jpg'
// import video_1 from '/@/assets/video/video1.mp4'
// import video_2 from '/@/assets/video/video2.mp4'
import code from '/@/assets/qr_code.png'

defineOptions({
  name: 'Portal',
})

const background = ref('')

const handleChange = (value) => {
  switch (value) {
    case 0: {
      background.value = `url('${carousel_1}')`
      break
    }
    case 1: {
      background.value = `url('${carousel_2}')`
      break
    }
    case 2: {
      background.value = `url('${carousel_3}')`
      break
    }

    default: {
      background.value = `url('${carousel_1}')`
      break
    }
  }
}

const list = ref([
  {
    title: '解决方案',
    description: ['全生命周期服务管理'],
    icon: 'lllustration/IconPark03',
  },
  {
    title: '合作伙伴',
    description: ['合作伙伴计划'],
    icon: 'lllustration/IconPark05',
  },
  {
    title: '服务支持',
    description: ['全方位优化产品体验'],
    icon: 'lllustration/IconPark07',
  },
  {
    title: '下载中心',
    description: ['下载手机端APP'],
    icon: 'lllustration/IconPark08',
  },
  {
    title: '联系我们',
    description: ['邮箱地址：<EMAIL>', '联系号码：400-8690-456'],
    icon: 'lllustration/IconPark04',
  },
  {
    title: '关注我们',
    img: true,
    description: ['二维码'],
    icon: 'lllustration/IconPark02',
  },
])

const openWindow = (url) => {
  window.open(url)
}

// const date = ref<any>(new Date())
// const tableData = [
//   {
//     date: '2016-05-03',
//     name: 'Tom',
//     address: 'No. 189, Grove St, Los Angeles',
//   },
//   {
//     date: '2016-05-02',
//     name: 'Tom',
//     address: 'No. 189, Grove St, Los Angeles',
//   },
//   {
//     date: '2016-05-04',
//     name: 'Tom',
//     address: 'No. 189, Grove St, Los Angeles',
//   },
//   {
//     date: '2016-05-01',
//     name: 'Tom',
//     address: 'No. 189, Grove St, Los Angeles',
//   },
//   {
//     date: '2016-05-01',
//     name: 'Tom',
//     address: 'No. 189, Grove St, Los Angeles',
//   },
//   {
//     date: '2016-05-01',
//     name: 'Tom',
//     address: 'No. 189, Grove St, Los Angeles',
//   },
//   {
//     date: '2016-05-01',
//     name: 'Tom',
//     address: 'No. 189, Grove St, Los Angeles',
//   },
//   {
//     date: '2016-05-01',
//     name: 'Tom',
//     address: 'No. 189, Grove St, Los Angeles',
//   },
//   {
//     date: '2016-05-01',
//     name: 'Tom',
//     address: 'No. 189, Grove St, Los Angeles',
//   },
//   {
//     date: '2016-05-01',
//     name: 'Tom',
//     address: 'No. 189, Grove St, Los Angeles',
//   },
//   {
//     date: '2016-05-01',
//     name: 'Tom',
//     address: 'No. 189, Grove St, Los Angeles',
//   },
// ]
</script>

<style lang="scss" scoped>
.portal-main {
  --portal-radius: 15px;

  @media screen and (max-width: 768px) {
    --portal-radius: 5px !important;
    main {
      width: 100% !important;
      padding: var(--el-padding) !important;

      :deep() {
        .el-carousel--horizontal {
          border-radius: var(--portal-radius) !important;
        }

        .icon-panel {
          border-radius: var(--portal-radius) !important;
        }
      }
    }
  }

  .carousel-background {
    position: absolute;
    top: 0;
    width: 100%;
    height: 180px;
    background: url('/@/assets/portal_images/carousel_1.jpg');
    filter: blur(100px);
    opacity: 0.5;
  }

  main {
    width: 1152px;
    padding: var(--el-padding) 0 0 0;
    margin-right: auto;
    margin-left: auto;
    border-top: 1px solid #f3f5f6;

    .left-tab {
      width: 100%;
      height: 420px;
      padding-top: 15px;
      background: #39364d;
      border-top-left-radius: 15px;

      :deep() {
        .el-menu-item.is-active {
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
    }

    :deep() {
      .el-carousel--horizontal {
        border-top-right-radius: 15px;
      }

      .el-calendar {
        padding: 0;
        margin-bottom: var(--el-margin);
        border-radius: var(--portal-radius);

        &-table {
          padding: 0;
        }

        &-day {
          height: 42px;
          line-height: 42px;
          text-align: center;
        }
      }

      .el-table {
        margin-bottom: var(--el-margin);
        border-radius: var(--portal-radius);
      }
    }

    .description-box {
      width: 100%;
      height: 120px;
      padding: 10px 20px 20px 20px;
      background: #fff;
      border: 1px solid var(--el-border-color);
      border-bottom-right-radius: 15px;
      border-bottom-left-radius: 15px;

      :deep() {
        .vab-divider--vertical {
          float: right;
          height: 6.5em;
          margin-top: -20px;
        }
      }

      h1 {
        margin-top: -10px;

        .clip {
          font-size: 32px;
          line-height: 0;
          background: linear-gradient(120deg, #bd34fe 30%, #41d1ff);
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .text {
        margin-top: -10px;
        font-size: 24px;
        font-weight: bold;
      }

      .show-box {
        padding-top: 25px;
        padding-left: 20px;

        .system-class-icon {
          float: left;
          width: 50px;
          height: 50px;
          line-height: 50px;
          color: var(--el-color-white);
          text-align: center;
          border-radius: 100%;

          [class*='ri'] {
            font-size: 24px;
          }
        }

        .describe {
          float: left;
          margin-top: 15px;
          margin-left: 20px;
          font-family: PingFangSC-Medium, serif;
          font-size: var(--el-font-size-medium);
          line-height: 22px;
          color: var(--el-color-grey);
          letter-spacing: 0.76px;
          white-space: nowrap;
        }
      }
    }

    .news-tit {
      h2 {
        font-size: 16px;
      }
    }

    .solution-box {
      position: relative;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 155px;
      padding: 0 16px 0 26px;
      margin-bottom: 32px;
      cursor: pointer;
      background-color: rgba(255, 255, 255, 0.6);
      border-radius: 10px;
      box-shadow: 0 12px 18px 2px rgba(204, 204, 204, 0.17);
      transition: transform 0.3s ease-out;
      .solution-box-item {
        .solution-box-description {
          white-space: nowrap;
        }
      }

      &:hover {
        transform: translateY(-10px);
      }

      &-title {
        margin-bottom: 16px;
        font-size: var(--el-font-size-extra-large);
        font-weight: 500;
        line-height: 28px;
      }

      &-description {
        line-height: 22px;
        color: #848b99;
      }

      :deep() {
        .vab-icon {
          width: 100px;
          height: 100px;
          margin-right: 0;
        }
      }
    }

    .news-img {
      border: 1px solid var(--el-border-color);
      border-radius: var(--portal-radius);
    }
  }
}

:deep() {
  .el-carousel__item:nth-of-type(1) {
    background: url('/@/assets/portal_images/carousel_1.jpg');
    background-size: cover;
  }

  .el-carousel__item:nth-of-type(2) {
    background: url('/@/assets/portal_images/carousel_2.jpg');
    background-size: cover;
  }

  .el-carousel__item:nth-of-type(3) {
    background: url('/@/assets/portal_images/carousel_3.jpg');
    background-size: cover;
  }

  .vab-footer {
    margin-top: 0;
    background: var(--el-background-color);
    border: 0;
  }

  .vab-theme-setting {
    section {
      > div {
        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4) {
          display: none;
        }
      }
    }
  }
}
</style>
