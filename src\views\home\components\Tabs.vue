<template>
  <div class="tabs">
    <vab-card class="tabs-card">
      <el-tabs v-model="activeName">
        <el-tab-pane label="超时未关锁" name="first">
          <!-- <template #label>
            <el-badge class="item" :value="6">
              <span>超时未关锁</span>
            </el-badge>
          </template> -->
          <el-table :data="tableData1">
            <el-table-column align="center" label="设备ID"  min-width="60" prop="device_id" />
            <el-table-column align="center" label="设备名称" min-width="50" prop="device_name" />
            <el-table-column align="center" label="区域" min-width="60" prop="province" >
              <template #default="{ row }">
                  {{ row.device_c_name }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="安装位置" min-width="80" prop="device_addr" />
            <el-table-column align="center" label="设备状态" min-width="80">
                <template #default="{ row }">
                  {{ switchFilter(row.device_status) }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="持续时间" prop="title" >
              <template #default="{ row }">
                {{ row.title.match(/\((\d+(\.\d+)?)小时\)/)[1] }}小时
              </template>
            </el-table-column>
            <el-table-column align="center" label="最近一次操作时间" prop="action_time" />
            <el-table-column align="center" label="最近一次操作人员" prop="username" />
            <template #empty>
              <el-empty class="vab-data-empty" description="暂无数据" />
            </template>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="长期未操作" name="second">
          <!-- <template #label>
            <el-badge class="item" :value="8">
              <span>长期未操作</span>
            </el-badge>
          </template> -->
          <el-table :data="tableData2">
            <el-table-column align="center" label="设备ID" min-width="60" prop="device_id" />
            <el-table-column align="center" label="设备名称" min-width="50" prop="device_name" />
            <el-table-column align="center" label="区域" min-width="60" prop="province" >
              <template #default="{ row }">
                {{ row.device_c_name }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="安装位置" min-width="80" prop="device_addr" />
            <el-table-column align="center" label="设备状态" min-width="80">
                <template #default="{ row }">
                  {{ switchFilter(row.device_status) }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="持续时间" prop="title" >
              <template #default="{ row }">
                {{ row.title.match(/\((\d+(\.\d+)?)天\)/)[1] }}天
              </template>
            </el-table-column>
            <el-table-column align="center" label="最近一次操作时间" prop="action_time" />
            <el-table-column align="center" label="最近一次操作人员" prop="username" />
            <template #empty>
              <el-empty class="vab-data-empty" description="暂无数据" />
            </template>
          </el-table>
        </el-tab-pane>
        <el-tab-pane  label="设备位置偏移" name="third">
          <!-- <template #label>
            <el-badge class="item" :value="8">
              <span>设备位置偏移</span>
            </el-badge>
          </template> -->
          <el-table :data="tableData3">
            <el-table-column align="center" label="设备ID" min-width="60" prop="device_id" />
            <el-table-column align="center" label="设备名称" min-width="50" prop="device_name" />
            <el-table-column align="center" label="区域" min-width="60" prop="province" >
              <template #default="{ row }">
                {{ row.device_c_name }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="安装位置" min-width="80" prop="device_addr" />
            <el-table-column align="center" label="设备状态" min-width="80">
                <template #default="{ row }">
                  {{ switchFilter(row.device_status) }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="最近一次操作时间" prop="action_time" />
            <el-table-column align="center" label="最近一次操作人员" prop="username" />
            <el-table-column align="center" label="偏移量" min-width="120" prop="title">
              <template #default="{ row }">
                {{ row.title.match(/\((\d+(\.\d+)?)米\)/)[1] }}米
              </template>
            </el-table-column>
            <template #empty>
              <el-empty class="vab-data-empty" description="暂无数据" />
            </template>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <el-button class="more" text type="primary" @click="toMore">查看更多</el-button>
    </vab-card>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { getAreaList } from '/@/api/system'
// 路由相关
const router = useRouter()

defineProps({
  tableData1: {
    type: Array,
    default: () => [],
  },
  tableData2: {
    type: Array,
    default: () => [],
  },
  tableData3: {
    type: Array,
    default: () => [],
  }
})
const activeName = ref('first')
const treeData = ref()

const getTreeData = async () => {
  const { data } = await getAreaList()
  treeData.value = data
}
// 开关过滤
const switchFilter = (status) => {
  if (status === '1') {
    return '关'
  } else if (status === '2') {
    return '开'
  }
}

// 跳转到更多告警
const toMore = () => {
  router.push({
    path: '/alarm/alarms',
  })
}
onBeforeMount(() => {
  getTreeData()
})
</script>

<style lang="scss" scoped>
.tabs-card {
  position: relative;
  .more {
    position: absolute;
    top: 15px;
    right: 10px;
  }
}
.item {
  padding: 0 12px 0 0;
}
</style>
