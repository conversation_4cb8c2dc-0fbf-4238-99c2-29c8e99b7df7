<template>
  <vab-card :body-style="{ height: '210px' }" skeleton>
    <template #header>
      <vab-icon icon="donut-chart-fill" />
      作业类型
    </template>
    <vab-chart :option="option" />
  </vab-card>
</template>

<script setup>
import { useSettingsStore } from '/@/store/modules/settings'

const settingsStore = useSettingsStore()
const { theme } = storeToRefs(settingsStore)
const props = defineProps({
  datas: {
    type: Object,
    default: () => {},
  }
})

const option = reactive({
  tooltip: {
    trigger: 'item',
  },
  series: [
    {
      name: '访问来源',
      type: 'pie',
      radius: ['40%', '75%'],
      itemStyle: {
        borderRadius: 8,
        borderColor: '#fff',
        borderWidth: 2,
      },
      data: [
        { value: Number(props.datas['巡检']) || 0, name: '巡检' },
        { value: Number(props.datas['抢修']) || 0, name: '抢修' },
        { value: Number(props.datas['工程']) || 0, name: '工程' },
        { value: Number(props.datas['装维']) || 0, name: '装维' },
        { value: Number(props.datas['检查']) || 0, name: '检查' },
      ],
    },
  ],
})

watch(
  theme.value,
  () => {
    option.color = [theme.value.color]
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped >
</style>
