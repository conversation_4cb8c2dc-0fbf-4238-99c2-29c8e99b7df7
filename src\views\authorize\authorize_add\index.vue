<template>
  <div class="step-form-container">
    <el-row :gutter="20" style="display: flex; flex-direction: column;">
      <el-col :lg="{ span: 10, offset: 7 }" :md="{ span: 20, offset: 2 }" :sm="{ span: 20, offset: 2 }" :xl="{ span: 10, offset: 7 }" :xs="24">
        <el-steps :active="active" align-center class="steps">
          <el-step description="选择授权人员" title="第1步" />
          <el-step description="选择授权设备并提交" title="第2步" />
          <el-step description="操作完成" title="第3步" />
        </el-steps>
      </el-col>
        <div v-show="active === 0" class="table-container">
          <el-button size="default" type="primary" @click="handleSetStep(1)">下一步</el-button>
          <el-row :gutter="20">
            <el-col :lg="5" :md="24" :sm="24" :xl="4" :xs="24">
              <vab-card class="auto-height-card">
                <el-input v-model="staffFilterText" placeholder="请输入查询条件" style="margin-bottom: 10px" />
                <el-scrollbar max-height="500px">
                  <el-tree
                    ref="staffTreeRef"
                    :data="staffTreeData"
                    default-expand-all
                    :expand-on-click-node="false"
                    :filter-node-method="filterStaffNode"
                    :props="{ children: 'children',label: 'name' }"
                    @node-click="handleStaffNodeClick"
                  />
                </el-scrollbar>
              </vab-card>
            </el-col>
            <el-col :lg="19" :md="24" :sm="24" :xl="20" :xs="24">
              <vab-query-form>
                <vab-query-form-left-panel :span="24">
                  <el-form inline :model="staffQueryForm" @submit.prevent>
                    <el-form-item  label="人员">
                      <el-input v-model.trim="staffQueryForm.phone_username" clearable placeholder="请输入人员姓名/手机号" />
                    </el-form-item>
                    <el-form-item>
                      <el-button :icon="Search" :loading="staffLoading" type="primary" @click="seachStaff">查询</el-button>
                    </el-form-item>
                    <el-form-item label="人员类型">
                      <div style="display: flex; gap: 8px">
                        <el-check-tag
                          v-for="(label, index) in labels"
                          :key="index"
                          :checked="checkedList[index]"
                          @change="onChange(index)"
                        >
                          {{ label }}
                        </el-check-tag>
                      </div>
                    </el-form-item>
                  </el-form>
                </vab-query-form-left-panel>
              </vab-query-form>
              <el-table ref="staffTableRef" v-loading="staffLoading" border :data="staffTable" row-key="id" @selection-change="setStaffRows">
                <el-table-column :reserve-selection="true" type="selection" width="38" />
                <el-table-column align="center" label="序号" width="55">
                  <template #default="{ $index }">
                    {{ $index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column align="center" label="姓名" prop="username" />
                <el-table-column align="center" label="手机号" prop="phone" />
                <el-table-column align="center" label="所属部门" prop="count" >
                  <template #default="{ row }">
                    {{ departmentFilter(row.dept_id) }}
                  </template>
                </el-table-column>
                <el-table-column align="center" label="人员类型" prop="count" >
                  <template #default="{ row }">
                    {{ statusFilter(row.user_type_id) }}
                  </template>
                </el-table-column>
                <!-- <el-table-column align="center" label="时间" min-width="160" prop="datetime" /> -->
                <el-empty v-if="staffEmptyShow" class="vab-data-empty" description="暂无数据" />
              </el-table>
              <vab-pagination :current-page="staffQueryForm.page" :page-size="staffQueryForm.page_size" :total="staffTotal" @current-change="handleStaffCurrentChange" @size-change="handleStaffSizeChange" />
            </el-col>
          </el-row>
        </div>
        <div v-show="active === 1" class="table-container">
          <el-button type="primary" @click="handleSubmit">提交</el-button>
          <el-button @click="handleSetStep(0)">上一步</el-button>
          <el-row :gutter="20">
            <el-col :lg="5" :md="24" :sm="24" :xl="4" :xs="24">
              <vab-card class="auto-height-card">
                <el-input v-model="deviceFilterText" placeholder="请输入查询条件" style="margin-bottom: 10px" />
                <el-scrollbar max-height="500px">
                  <el-tree
                    ref="deviceTreeRef"
                    :data="deviceTreeData"
                    default-expand-all
                    :filter-node-method="filterDeviceNode"
                    :props="{ children: 'children',label: 'name' }"
                    @node-click="handleDeviceNodeClick"
                  />
                </el-scrollbar>
              </vab-card>
            </el-col>
            <el-col :lg="19" :md="24" :sm="24" :xl="20" :xs="24">
              <vab-query-form>
                <vab-query-form-left-panel :span="24">
                  <el-form inline :model="deviceQueryForm" @submit.prevent>
                    <el-form-item label="设备">
                      <el-input v-model.trim="deviceQueryForm.name" clearable placeholder="请输入设备名称" />
                    </el-form-item>
                    <el-form-item>
                      <el-button :icon="Search" :loading="deviceLoading" type="primary" @click="seachDevice">查询</el-button>
                    </el-form-item>
                  </el-form>
                </vab-query-form-left-panel>
              </vab-query-form>
              <el-table ref="deviceTableRef" v-loading="deviceLoading" border :data="deviceTable" row-key="id" @selection-change="setDeviceRows">
                <el-table-column :reserve-selection="true" type="selection" width="38" />
                <el-table-column align="center" label="序号" width="55">
                  <template #default="{ $index }">
                    {{ $index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column align="center" label="设备ID" prop="id" />
                <el-table-column align="center" label="设备名称" prop="name" />
                <el-table-column align="center" label="设备区域" prop="count" >
                  <template #default="{ row }">
                    {{ areaFilter(row.c_id) }}
                  </template>
                </el-table-column>
                <el-table-column align="center" label="安装位置" min-width="160" prop="addr" />
                <el-table-column align="center" label="备注" min-width="160" prop="remark" />
                <el-empty v-if="deviceEmptyShow" class="vab-data-empty" description="暂无数据" />
              </el-table>
              <vab-pagination :current-page="deviceQueryForm.page" :page-size="deviceQueryForm.page_size" :total="deviceTotal" @current-change="handleDeviceCurrentChange" @size-change="handleDeviceSizeChange" />
            </el-col>
          </el-row>
        </div>
        <div v-show="active === 2" class="success-container">
          <div class="pay-top-content">
            <vab-icon class="pay-success" icon="checkbox-circle-line" />
            <p>授权成功</p>
          </div>
          <el-button size="default" type="primary" @click="handleSuccess">完成</el-button>
        </div>
    </el-row>
  </div>
</template>

<script setup>
import {  onBeforeMount, reactive, ref, watch } from 'vue'
import { useTabsStore } from '/@/store/modules/tabs'
import { handleActivePath } from '/@/utils/routes'
import { Search } from '@element-plus/icons-vue'
import { getPersonnelList } from '/@/api/personnel'
import { getDeviceList } from '/@/api/device'
import { getAreaList, getDepartmentList } from '/@/api/system'
import { onAuthorize, revokeAuthorize } from '/@/api/authorize'

defineOptions({
  name: 'StepForm',
})

const route = useRoute()
const tabsStore = useTabsStore()
const { changeTabsMeta, delVisitedRoute } = tabsStore

// 人员
const staffTableRef = ref(null)
const staffTable = ref([])
const staffSelectRows = ref([])
const staffLoading = ref(true)
const staffEmptyShow = ref(true)
const staffQueryForm = reactive({
  page: 1,
  page_size: 10,
  dept_id: 0,
  phone_username: '',
  user_type_id: []
})
const staffTotal = ref(0)
const staffTreeRef = ref(null)
const staffTreeData = ref([])
const staffFilterText = ref('')
// const labels = ref(['工程', '抢修', '巡检', '装维', '检查人员']) // 金温铁路
const labels = ref(['工程', '抢修', '巡检', '检查人员']) // 铁塔
// const checkedList = reactive([false, false, false, false, false]) // 金温铁路
const checkedList = reactive([false, false, false, false]) // 铁塔

// 设备
const deviceTableRef = ref()
const deviceTable = ref([])
const deviceSelectRows = ref([])
const deviceLoading = ref(true)
const deviceEmptyShow = ref(true)
const deviceQueryForm = reactive({
  page: 1,
  page_size: 10,
  c_id: 0,
  name: '',
})
const deviceTotal = ref(0)
const deviceTreeRef = ref(null)
const deviceTreeData = ref([])
const deviceFilterText = ref('')

const active = ref(0)
// 树过滤
watch(staffFilterText, (value) => {
  staffTreeRef.value?.filter(value)
})
watch(deviceFilterText, (value) => {
  deviceTreeRef.value?.filter(value)
})

const filterStaffNode = (value, data) => {
  if (!value) return true
  return data.name.includes(value)
}

const filterDeviceNode = (value, data) => {
  if (!value) return true
  return data.name.includes(value)
}

// 获取工作人员数据
const getStaff = async () => {
  staffLoading.value = true
  const response = await getPersonnelList({...staffQueryForm, user_type_id: JSON.stringify(staffQueryForm.user_type_id)})
  staffTable.value = response.data.list
  staffTotal.value = response.data.total
  staffLoading.value = false
}

// 获取设备数据
const getDevice = async () => {
  deviceLoading.value = true
  const response = await getDeviceList(deviceQueryForm)
  deviceTable.value = response.data.list
  deviceTotal.value = response.data.total
  deviceLoading.value = false
}

// 获取部门数据
const getDepartmentTree  = async () => {
  const { data } = await getDepartmentList()
  staffTreeData.value = data
}

// 获取区域数据
const getAreaTree  = async () => {
  const { data } = await getAreaList()
  deviceTreeData.value = data
}

// 人员部门数据过滤
const departmentFilter = (id) => {
  const searchTree = (nodes, id) => {
    for (const node of nodes) {
      if (node.id === id) {
        return node.name; // 找到节点，返回 name
      }
      if (node.children) {
        const result = searchTree(node.children, id); // 递归查找子节点
        if (result) {
          return result; // 如果子节点找到，则返回结果
        }
      }
    }
    return null; // 未找到，返回 null
  };
  const result = searchTree(staffTreeData.value, id);
  return result
}

// 人员类型状态过滤
const statusFilter = (status) => {
  switch (status) {
  case 1: {
    return '工程'
  }
  case 2: {
    return '抢修'
  }
  case 3: {
    return '巡检'
  }
  case 4: {
    return '装维'
  }
  case 5: {
    return '检察人员'
  }
  // No default
  }
}

// 设备区域数据过滤
const areaFilter = (id) => {
  const searchTree = (nodes, id) => {
    for (const node of nodes) {
      if (node.id === id) {
        return node.name; // 找到节点，返回 name
      }
      if (node.children) {
        const result = searchTree(node.children, id); // 递归查找子节点
        if (result) {
          return result; // 如果子节点找到，则返回结果
        }
      }
    }
    return null; // 未找到，返回 null
  };
  const result = searchTree(deviceTreeData.value, id);
  return result
}

// 查询人员
const seachStaff = () => {
  staffQueryForm.page = 1
  getStaff()
}

// 根据类型查询人员
// 选择人员类型
const onChange = (index) => {
  checkedList[index] = !checkedList[index]
  const val = index + 1
  if (checkedList[index]) {
    staffQueryForm.user_type_id.push(val)
  } else {
    staffQueryForm.user_type_id = staffQueryForm.user_type_id.filter(id => id !== val)
  }
  staffQueryForm.page = 1
  getStaff()
}
// const onChange = (val) => {
//   switch (val) {
//   case 1: {
//     checked1.value = !checked1.value
//     if (checked1.value) {
//       staffQueryForm.user_type_id.push(val)
//     } else {
//       staffQueryForm.user_type_id = staffQueryForm.user_type_id.filter((id) => id !== val)
//     }
//   break;
//   }
//   case 2: {
//     checked2.value = !checked2.value
//     if (checked2.value) {
//       staffQueryForm.user_type_id.push(val)
//     } else {
//       console.log(val);
//       staffQueryForm.user_type_id = staffQueryForm.user_type_id.filter((id) => id !== val)
//       console.log(staffQueryForm.user_type_id);
//     }
//   break;
//   }
//   case 3: {
//     checked3.value = !checked3.value
//     if (checked3.value) {
//       staffQueryForm.user_type_id.push(val)
//     } else {
//       staffQueryForm.user_type_id = staffQueryForm.user_type_id.filter((id) => id !== val)
//     }
//   break;
//   }
//   case 4: {
//     checked4.value = !checked4.value
//     if (checked4.value) {
//       staffQueryForm.user_type_id.push(val)
//     } else {
//       staffQueryForm.user_type_id = staffQueryForm.user_type_id.filter((id) => id !== val)
//     }
//   break;
//   }
//   case 5: {
//     checked5.value = !checked5.value
//     if (checked5.value) {
//       staffQueryForm.user_type_id.push(val)
//     } else {
//       staffQueryForm.user_type_id = staffQueryForm.user_type_id.filter((id) => id !== val)
//     }
//   break;
//   }
//   // No default
//   }
//   staffQueryForm.page = 1
//   getStaff()
// }

// 查询设备
const seachDevice = () => {
  deviceQueryForm.page = 1
  getDevice()
}

// 选择部门查询
const handleStaffNodeClick = (value) => {
  staffQueryForm.dept_id = value.parent_id
  getStaff()
}

// 选择区域查询
const handleDeviceNodeClick = (value) => {
  deviceQueryForm.c_id = value.parent_id
  getDevice()
}

// 选择工作人员
const setStaffRows = (value) => {
  staffSelectRows.value = value
}

// 选择设备
const setDeviceRows = (value) => {
  deviceSelectRows.value = value
}

// 工作人员改变每页显示数据量
const handleStaffSizeChange = (value) => {
  staffQueryForm.page = 1
  staffQueryForm.page_size = value
  getStaff()
}

// 设备改变每页显示数据量
const handleDeviceSizeChange = (value) => {
  deviceQueryForm.page = 1
  deviceQueryForm.page_size = value
  getDevice()
}

// 工作人员改变页码
const handleStaffCurrentChange = (value) => {
  staffQueryForm.page = value
  getStaff()
}

// 工作人员改变页码
const handleDeviceCurrentChange = (value) => {
  deviceQueryForm.page = value
  getDevice()
}

const handleSetStep = (_active) => {
  if (_active === 1) {
    if (staffSelectRows.value.length > 0) {
      active.value = _active
    } else {
      $baseMessage('请至少选择一位人员', 'warning', 'hey')
    }
  } else if (_active === 0) {
    active.value = _active
  }
  // active.value = _active
}

const handleSubmit = async() => {
  if (route.query.type === '1') {
    if (deviceSelectRows.value.length > 0) {
      await staffSelectRows.value.map((staff) => {
        deviceSelectRows.value.map(async (device) => {
          await onAuthorize({ user_id: staff.id, device_id: device.id, expires_type: 1})
        })
      })
      active.value = 2
    } else {
      $baseMessage('请至少选择一个设备', 'warning', 'hey')
    }
  } else if (route.query.type === '2') {
    if (deviceSelectRows.value.length > 0) {
      await staffSelectRows.value.map((staff) => {
        deviceSelectRows.value.map(async (device) => {
          await revokeAuthorize({ user_id: staff.id, device_id: device.id, expires_type: 1})
        })
      })
      active.value = 2
    } else {
      $baseMessage('请至少选择一个设备', 'warning', 'hey')
    }
  }
}

const handleSuccess = () => {
  delVisitedRoute(handleActivePath(route, true))
  history.back()
}

onBeforeMount(() => {
  changeTabsMeta({
    title: '授权详情',
    meta: {
      title: `${route.query.type === '1' ? '批量授权' : '批量取消'}`,
    },
  })
  getStaff()
  getDepartmentTree()
  getDevice()
  getAreaTree()
})
</script>

<style lang="scss" scoped>
@import './index.scss'
</style>
