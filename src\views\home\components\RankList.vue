<!-- eslint-disable vue/no-parsing-error -->
<template>
  <vab-card :body-style="{ padding: '0 20px' }">
    <template #header>
      <vab-icon icon="medal-line" />
      个人操作次数排行
    </template>
    <div v-if="datas" class="medal-list">
      <div v-for="(item, index) in datas.slice(0, 5)" :key="index" class="medal-list-item">
        <div class="medal-list-item-rank"></div>
        <div class="medal-list-item-right">
          <div style="width: 60px;">{{ item.username }}</div><div style="width: 60px;">{{ statusFilter(item.user_type_id) }}</div><div>{{ item.action_num }} 次</div>
        </div>
      </div>
    </div>
  </vab-card>
</template>#

<script setup>
defineProps({
  datas: {
    type: Object,
    default: () => {},
  }
})
// 状态过滤
const statusFilter = (status) => {
  switch (status) {
  case 1: {
    return '工程'
  }
  case 2: {
    return '抢修'
  }
  case 3: {
    return '巡检'
  }
  case 4: {
    return '装维'
  }
  case 5: {
    return '检查人员'
  }
  // No default
  }
}
</script>

<style lang="scss" scoped>
.medal-list {
  &-item {
    position: relative;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    width: 100%;
    height: 44.5px;
    clear: both;
    background: var(--el-color-white);
    border-radius: 0;

    $position: (
      1: -5px -122px,
      2: -64px -83px,
      3: -123px -5px,
      4: -123px -39px,
      5: -123px -73px,
    );

    @each $key, $item in $position {
      &:nth-child(#{$key}) {
        .medal-list-item-rank {
          background-position: $item;
        }
      }
    }

    &-img {
      float: left;
      margin: 10px 16px 25px 56px;
    }

    &-img > div {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 56px;
      height: 56px;
      margin: auto;
      line-height: 56px;
      text-align: center;
      border-radius: 12px;

      i {
        display: block;
        margin: auto;
        font-size: 30px;
        color: var(--el-color-white);
      }
    }

    &-rank {
      // position: absolute;
      // top: 26px;
      // left: 20px;
      width: 24px;
      height: 24px;
      margin-right: 30px;
      background-image: url('/@/assets/rank_images/rank.png');
      background-size: 152px 151px;
    }

    &-right {
      flex: 1;
      height: 48px;
      // margin: 15px 0 0 -5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .medal-list-item-right {
        color: var(--el-color-grey);
      }

      .item-type {
        display: inline-block;
        padding: 0 8px;
        font-size: 14px;
        line-height: 20px;
        color: var(--el-color-white);
        background: var(--el-color-warning);
        border-radius: var(--el-border-radius-base);
      }
    }
  }
}
</style>
