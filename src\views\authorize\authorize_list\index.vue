<template>
  <div class="list-container auto-height-container" :class="{ 'fullscreen-container': isFullscreen }">
    <vab-query-form>
      <vab-query-form-left-panel :span="20">
        <el-form inline :model="queryForm" @submit.prevent>
          <el-form-item label="标题">
            <el-input v-model.trim="queryForm.title" clearable placeholder="请输入标题" />
          </el-form-item>
          <el-form-item>
            <el-button :icon="Search" :loading="listLoading" type="primary" @click="queryData">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button :icon="Plus" type="primary" @click="addAuthorize">批量授权</el-button>
            <el-button :icon="Delete" type="danger" @click="cancelAuthorize">批量取消</el-button>
            <el-button :icon="Pointer" :type="allPassStatus ? 'warning' : 'success'" @click="onAllPassDevice">
              {{ allPassStatus ? "取消一键授权" : "应急一键授权" }}
            </el-button>
            <div class="prompt-text">
              <span v-if="allPassStatus">一键授权生效中</span>
              <span v-else>正常授权生效中</span>
            </div>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <!-- <vab-query-form-left-panel :span="20">
        <el-button :icon="Plus" type="primary" @click="addAuthorize">批量授权</el-button>
        <el-button :icon="Delete" type="danger" @click="cancelAuthorize">批量取消</el-button>
        <el-button :icon="Pointer" type="success" @click="onTest">应急一键授权</el-button>
      </vab-query-form-left-panel> -->
      <vab-query-form-right-panel :span="4">
        <div class="custom-table-right-tools">
          <el-button class="hidden-xs-only">
            <el-checkbox v-model="stripe" label="斑马纹" />
          </el-button>
          <el-button class="hidden-xs-only">
            <el-checkbox v-model="border" label="边框" />
          </el-button>
          <el-button @click="queryData">
            <vab-icon icon="refresh-line" />
          </el-button>
          <el-button @click="clickFullScreen">
            <vab-icon :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'" />
          </el-button>
          <el-popover :width="165">
            <el-radio-group v-model="lineHeight">
              <el-radio-button label="large" value="large">大</el-radio-button>
              <el-radio-button label="default" value="default">中</el-radio-button>
              <el-radio-button label="small" value="small">小</el-radio-button>
            </el-radio-group>
            <template #reference>
              <el-button>
                <vab-icon icon="line-height" />
              </el-button>
            </template>
          </el-popover>
          <el-popover popper-class="custom-table-checkbox">
            <template #reference>
              <el-button>
                <vab-icon icon="settings-line" />
              </el-button>
            </template>
            <vab-draggable v-model="columns" :animation="600" target=".el-checkbox-group">
              <el-checkbox-group v-model="checkList">
                <el-checkbox
                  v-for="item in columns"
                  :key="item.label"
                  :disabled="item.disableCheck"
                  :label="item.label"
                  :value="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </el-checkbox-group>
            </vab-draggable>
          </el-popover>
        </div>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableRef"
      v-loading="listLoading"
      :border="border"
      :data="list"
      :size="lineHeight"
      :stripe="stripe"
    >
      <el-table-column align="center" label="序号" width="55">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :fixed="item.fixed"
        :label="item.label"
        :min-width="item.minWidth || 105"
        :prop="item.prop"
        show-overflow-tooltip
        :sortable="item.sortable"
      >
        <template #default="{ row }">
          <span v-if="item.label === '申请期限'">
            {{ expiresFilter(row[item.prop]) }}
          </span>
          <span v-if="item.label === '申请状态'" placement="top-start">
            <span v-if="row[item.prop] === '1'" style="color: #E6A23C">审核中</span>
            <span v-if="row[item.prop] === '2'" style="color: #67C23A;">同意</span>
            <span v-if="row[item.prop] === '3'" style="color: #F56C6C;">拒绝</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" :width="215">
        <template #default="{ row }">
          <div v-if="row.is_pass === '1'">
            <el-button type="primary" @click="handleAgree(row)">同意</el-button>
            <el-button type="danger" @click="handleRefuse(row)">拒绝</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty class="vab-data-empty" description="暂无数据" />
      </template>
    </el-table>
    <vab-pagination
      :current-page="queryForm.page"
      :page-size="queryForm.page_size"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup>
import { Delete, Plus, Pointer, Search } from '@element-plus/icons-vue'
import { VueDraggable as VabDraggable } from 'vue-draggable-plus'
import { getList, toExamine, getAllPassStatus, setAllPassDevice, cancelAllPassDevice } from '/@/api/authorize'

const router = useRouter()

defineOptions({
  name: 'List',
})

const tableRef = ref(null)
const stripe = ref(false)
const border = ref(false)
const lineHeight = ref('small')
const isFullscreen = ref(false)
const allPassStatus = ref(false);
// 全屏处理
const { exit, enter, isFullscreen: _isFullscreen } = useFullscreen()

const columns = ref([
  {
    label: '设备ID',
    prop: 'entity_id',
    sortable: false,
    checked: true,
  },
  {
    label: '申请设备',
    prop: 'device_name',
    sortable: false,
    checked: true,
  },
  {
    label: '申请人',
    prop: 'username',
    sortable: false,
    checked: true,
  },
  {
    label: '申请时间',
    prop: 'created_at',
    sortable: true,
    checked: true,
  },
  {
    label: '申请期限',
    prop: 'expires_type',
    sortable: true,
    checked: true,
  },
  {
    label: '授权人员',
    prop: 'passer_username',
    sortable: false,
    checked: true,
  },
  {
    label: '授权时间',
    prop: 'pass_time',
    sortable: true,
    checked: true,
  },
  {
    label: '申请状态',
    prop: 'is_pass',
    sortable: false,
    checked: true,
  },
])
const checkList = ref([])
const list = ref([])
const total = ref(0)
const queryForm = reactive({
  page: 1,
  page_size: 20,
  title: '',
})

const listLoading = ref(true)
const emptyShow = ref(true)

const finallyColumns = computed(() => columns.value.filter((item) => checkList.value.includes(item.label)))

const fetchData = async () => {
  listLoading.value = true
  const { data: data1 } = await getList(queryForm)
  list.value = data1.list
  total.value = data1.total
  listLoading.value = false
  emptyShow.value = data1.total <= 0

  const { data: data2 } = await getAllPassStatus({})
  allPassStatus.value = data2.data
}

const handleSizeChange = (value) => {
  queryForm.page = 1
  queryForm.page_size = value
  fetchData()
}

const handleCurrentChange = (value) => {
  queryForm.page = value
  fetchData()
}

// 类型过滤
const expiresFilter = (value) => {
  switch (value) {
  case 1: {
    return '长期'
  }
  case 2: {
    return '24小时'
  }
  case 3: {
    return '48小时'
  }
  case 4: {
    return '72小时'
  }
  // No default
  }
}

const queryData = () => {
  queryForm.page = 1
  fetchData()
}

// 全屏切换
const clickFullScreen = () => {
  isFullscreen.value = !isFullscreen.value
  isFullscreen.value ? enter() : exit()
}

const handleAgree = (row) => {
  console.log(row)
  $baseConfirm('您确定要同意授权吗',  null, async () => {
    await toExamine({ id: row.id, is_pass: 2 })
    $baseMessage('已同意授权', 'success', 'hey')
    await fetchData()
  })
}

const handleRefuse = (row) => {
  console.log(row)
  $baseConfirm('您确定要拒绝授权吗', null, async () => {
    await toExamine({ id: row.id, is_pass: 3 })
    $baseMessage('已拒绝授权', 'success', 'hey')
    await fetchData()
  })
}

const addAuthorize = () => {
    router.push({
      path: '/authorize/authorizesAdd',
      query: {
        type: 1,
      },
    })
}

const cancelAuthorize = () => {
    router.push({
      path: '/authorize/authorizesAdd',
      query: {
        type: 2,
      },
    })
}

const onAllPassDevice = async () => {
  if (allPassStatus.value) {
    const { data } = await cancelAllPassDevice({})
  } else {
    const { data } = await setAllPassDevice({})
  }
  fetchData()
}

// 监听全屏状态
watch(
  _isFullscreen,
  () => {
    isFullscreen.value = _isFullscreen.value
  },
  { immediate: true }
)

onBeforeMount(() => {
  columns.value.forEach((item) => {
    if (item.checked) checkList.value.push(item.label)
  })
  fetchData()
})
</script>

<style lang="scss" scoped>
@import './index.scss'
</style>
