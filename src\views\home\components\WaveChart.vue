<template>
  <vab-card :body-style="{ height: '210px' }" skeleton>
    <template #header>
      <vab-icon icon="line-chart-fill" />
      月度操作次数
    </template>
    <vab-chart :option="option" />
  </vab-card>
</template>

<script setup>
import { useSettingsStore } from '/@/store/modules/settings'
import { lightenColor } from '/@/utils/lightenColor'

const settingsStore = useSettingsStore()
const { theme } = storeToRefs(settingsStore)

const props = defineProps({
  datas: {
    type: Object,
    default: () => {},
  }
})

const option = reactive({
  tooltip: {
    trigger: 'axis',
    extraCssText: 'z-index:1',
  },
  grid: {
    top: '4%',
    left: '2%',
    right: '2%',
    bottom: '0%',
    containLabel: true,
  },
  xAxis: [
    {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      boundaryGap: false,
    },
  ],
  yAxis: [
    {
      type: 'value',
    },
  ],
  series: [
    {
      name: '开锁次数',
      type: 'line',
      data: Object.values(props.datas),
      symbol: 'circle',
      smooth: true,
      yAxisIndex: 0,
      showSymbol: false,
      areaStyle: {
        opacity: 0.8,
      },
    }
  ],
})

watch(
  theme.value,
  () => {
    option.color = [theme.value.color, lightenColor(theme.value.color, 50)]
  },
  { immediate: true }
)
</script>
