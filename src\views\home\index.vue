<template>
  <div class="dashboard-container no-background-container">
    <tiny-scroll-text v-if="countConfig3.percentage !== 0" class="vab-scroll-text-primary" direction="left" :time="20">
      <div v-html="text"></div>
    </tiny-scroll-text>
    <el-row :gutter="20">
      <el-col :lg="3" :md="12" :sm="24" :xl="3" :xs="24">
        <vab-card class="dashboard-user">
          <vab-icon icon="lllustration/Scenes03" is-custom-svg />
        </vab-card>
      </el-col>
      <el-col :lg="7" :md="12" :sm="24" :xl="7" :xs="24">
        <wy-card background="white" :count-config="countConfig1" icon="lock-password-line" title="设备数量" />
      </el-col>
      <el-col :lg="7" :md="12" :sm="24" :xl="7" :xs="24">
        <wy-card background="white" :count-config="countConfig2" icon="user-2-line" title="人员数量" />
      </el-col>
      <el-col :lg="7" :md="12" :sm="24" :xl="7" :xs="24">
        <wy-card background="white" :count-config="countConfig3" icon="file-list-line" title="开锁申请" />
      </el-col>
      <el-col :lg="7" :md="12" :sm="24" :xl="7" :xs="24">
        <pie-chart v-if="homeData.work_type" :datas="homeData.work_type" />
      </el-col>
      <el-col :lg="9" :md="12" :sm="24" :xl="9" :xs="24">
        <wave-chart v-if="homeData.open_log_num" :datas="homeData.open_log_num" />
      </el-col>
      <el-col :lg="8" :md="24" :sm="24" :xl="8" :xs="24">
        <ranking v-if="homeData.locking_ranking" :datas="homeData.locking_ranking" />
      </el-col>
      <el-col :lg="18" :md="18" :sm="24" :xl="18" :xs="24">
        <distribution v-if="homeData.get_record_distribution" :datas="homeData.get_record_distribution" />
      </el-col>
      <el-col :lg="6" :md="6" :sm="24" :xl="6" :xs="24">
        <rank-list :datas="homeData.User_d" />
      </el-col>
      <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
        <tabs :table-data1="tableData1" :table-data2="tableData2" :table-data3="tableData3" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ScrollText as TinyScrollText } from '@opentiny/vue'
import WyCard from '/@/views/home/<USER>/WyCard.vue'
import PieChart from '/@/views/home/<USER>/PieChart.vue';
import WaveChart from '/@/views/home/<USER>/WaveChart.vue';
import Ranking from '/@/views/home/<USER>/Ranking.vue';
import Distribution from '/@/views/home/<USER>/Distribution.vue';
import RankList from '/@/views/home/<USER>/RankList.vue';
import Tabs from '/@/views/home/<USER>/Tabs.vue';
import { getHomeData, getHomeList } from '/@/api/home'

defineOptions({
  name: 'Dashboard',
})

const text = ref()
const homeData = ref({})
const countConfig1 = ref({
  subTitle: '处于开锁状态',
  startValue: 0,
  endValue: 1,
  decimals: 0,
  prefix: '',
  separator: ',',
  duration: 8000,
  percentage: 0
})

const countConfig2 = ref({
  subTitle: '今日使用人员',
  startValue: 0,
  endValue: 2,
  decimals: 0,
  prefix: '',
  separator: ',',
  duration: 8000,
  percentage: 0
})

const countConfig3 = ref({
  subTitle: '待处理',
  startValue: 0,
  endValue: 3,
  decimals: 0,
  prefix: '',
  separator: ',',
  duration: 8000,
  percentage: 0
})

const tableData1 = ref([])
const tableData2 = ref([])
const tableData3 = ref([])

const getData = async() => {
  const { data } = await getHomeData()
  homeData.value = data
  countConfig1.value.endValue = data.device_num.lock_num
  countConfig1.value.percentage = data.device_num.open_status_num
  countConfig2.value.endValue = data.user_num.user_num
  countConfig2.value.percentage = data.user_num.user_use_num
  countConfig3.value.endValue = data.approval_num.approval_num
  countConfig3.value.percentage = data.approval_num.approval_pending_num
  text.value=`🔊 你有<span style="color: #F56C6C">${data.approval_num.approval_pending_num}</span>个开锁申请待处理`
}
const getOneTable = async() => {
  const { data } = await getHomeList({ page: 1, page_size: 10, alarm_type: 1})
  tableData1.value = data.list
}
const getTwoTable = async() => {
  const { data } = await getHomeList({ page: 1, page_size: 10, alarm_type: 2})
  tableData2.value = data.list
}
const getThreeTable = async() => {
  const { data } = await getHomeList({ page: 1, page_size: 10, alarm_type: 3})
  tableData3.value = data.list
}
const getTableList = async() => {
  await getOneTable()
  await getTwoTable()
  await getThreeTable()
}
onBeforeMount(() => {
  getData()
  getTableList()
})
</script>

<style lang="scss" scoped>
@import './index.scss'
</style>
