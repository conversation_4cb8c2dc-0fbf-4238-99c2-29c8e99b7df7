<template>
  <div class="device-table-container no-background-container auto-height-container" :class="{ 'fullscreen-container': isFullscreen }">
    <el-row :gutter="20">
      <el-col :lg="4" :md="24" :sm="24" :xl="3" :xs="24">
        <vab-card class="auto-height-card">
          <el-input v-model="filterText" placeholder="请输入查询条件" style="margin-bottom: 10px" />
          <el-scrollbar max-height="600px">
            <el-tree
              ref="treeRef"
              :data="treeData"
              default-expand-all
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              :props="{ children: 'children',label: 'name' }"
              @node-click="handleNodeClick"
            />
          </el-scrollbar>
        </vab-card>
      </el-col>
      <el-col :lg="20" :md="24" :sm="24" :xl="21" :xs="24">
        <vab-card class="auto-height-card">
          <vab-query-form>
            <vab-query-form-top-panel :span="20">
              <el-form inline :model="queryForm" @submit.prevent>
                <el-form-item label="设备名称">
                  <el-input v-model="queryForm.name" clearable placeholder="请输入设备名称" />
                </el-form-item>
                <el-form-item label="开关状态">
                  <el-select v-model="queryForm.status" clearable placeholder="请选择开关状态">
                    <el-option label="开" :value="2" />
                    <el-option label="关" :value="1" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button :icon="Search" :loading="listLoading" native-type="submit" type="primary" @click="queryData">查询</el-button>
                </el-form-item>
              </el-form>
            </vab-query-form-top-panel>
            <vab-query-form-left-panel :span="20">
              <el-button :icon="Plus" type="primary" @click="handleAdd">新增</el-button>
              <el-button :icon="Upload" type="success" @click="handleUpload">批量导入</el-button>
              <el-button :icon="Download" type="info" @click="handleDownload">下载模板</el-button>
              <!-- <el-button :icon="MapLocation" type="primary" @click="handlMap">地图模式</el-button> -->
              <el-button type="primary" @click="handle3DMode">3D模式</el-button>
            </vab-query-form-left-panel>
            <vab-query-form-right-panel :span="4">
              <div class="custom-table-right-tools">
                <el-button class="hidden-xs-only">
                  <el-checkbox v-model="stripe" label="斑马纹" />
                </el-button>
                <el-button class="hidden-xs-only">
                  <el-checkbox v-model="border" label="边框" />
                </el-button>
                <el-button @click="queryData">
                  <vab-icon icon="refresh-line" />
                </el-button>
                <el-button @click="clickFullScreen">
                  <vab-icon :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'" />
                </el-button>
                <el-popover :width="165">
                  <el-radio-group v-model="lineHeight">
                    <el-radio-button label="large" value="large">大</el-radio-button>
                    <el-radio-button label="default" value="default">中</el-radio-button>
                    <el-radio-button label="small" value="small">小</el-radio-button>
                  </el-radio-group>
                  <template #reference>
                    <el-button>
                      <vab-icon icon="line-height" />
                    </el-button>
                  </template>
                </el-popover>
                <el-popover popper-class="custom-table-checkbox">
                  <template #reference>
                    <el-button>
                      <vab-icon icon="settings-line" />
                    </el-button>
                  </template>
                  <vab-draggable v-model="columns" :animation="600" target=".el-checkbox-group">
                    <el-checkbox-group v-model="checkList">
                      <el-checkbox
                        v-for="item in columns"
                        :key="item.label"
                        :disabled="item.disableCheck"
                        :label="item.label"
                        :value="item.label"
                      >
                        {{ item.label }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </vab-draggable>
                </el-popover>
              </div>
            </vab-query-form-right-panel>
          </vab-query-form>
          <el-table
            ref="tableRef"
            v-loading="listLoading"
            :border="border"
            :data="list"
            :size="lineHeight"
            :stripe="stripe"
            @selection-change="setSelectRows"
            @sort-change="sortChange"
          >
            <el-table-column type="selection" width="38" />
            <!-- <el-table-column align="center" label="序号" width="55">
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column> -->
            <el-table-column
              v-for="(item, index) in finallyColumns"
              :key="index"
              align="center"
              :fixed="item.fixed"
              :label="item.label"
              :min-width="item.minWidth || 105"
              :prop="item.prop"
              show-overflow-tooltip
              :sortable="item.sortable"
            >
              <template #default="{ row }">
                <span v-if="item.label === '区域'">
                  {{ departmentFilter(row[item.prop]) }}
                </span>
                <span v-if="item.label === '开关状态'">
                  <span v-if="row[item.prop] === 1" style="color: #F56C6C;">关</span>
                  <span v-if="row[item.prop] === 2" style="color: #67C23A;">开</span>
                </span>
                <span v-if="item.label === '使用情况'" placement="top-start">
                  <span v-if="row[item.prop] === 1">未配置</span>
                  <span v-if="row[item.prop] === 2" style="color: #F56C6C;">配置失效</span>
                  <span v-if="row[item.prop] === 3">正常</span>
                  <span v-if="row[item.prop] === 4" style="color: #F56C6C;">故障</span>
                </span>
                <span v-if="item.label === '偏移量'">
                  {{ row[item.prop] }}米
                </span>
                <!-- <span v-else>{{ row[item.prop] }}</span> -->
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="155">
              <template #default="{ row }">
                <el-button text type="primary" @click="handleDetail(row)">详情</el-button>
                <el-button text type="primary" @click="handleEdit(row)">编辑</el-button>
                <!-- <el-button text type="danger" @click="handleDelete(row)">删除</el-button> -->
              </template>
            </el-table-column>
            <template #empty>
              <el-empty class="vab-data-empty" description="暂无数据" />
            </template>
          </el-table>
          <vab-pagination
            :current-page="queryForm.page"
            :page-size="queryForm.page_size"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          />
        </vab-card>
      </el-col>
    </el-row>
    <device-table-edit ref="editRef" :tree-data="treeData" @fetch-data="fetchData" />
  </div>
</template>

<script setup>
import { onActivated, onBeforeMount, reactive, ref, watch } from 'vue'

import DeviceTableEdit from '/@/views/device/device_edit/index.vue'

import { useRouter } from 'vue-router'
import { VueDraggable as VabDraggable } from 'vue-draggable-plus'
import { Download, MapLocation, Plus, Search, Upload } from '@element-plus/icons-vue'
import { ElTree } from 'element-plus'
import { downLoadMb, getDeviceList, onImport, upLoadFile } from '/@/api/device'
import { getAreaList } from '/@/api/system'
// import { useRoutesStore } from '/@/store/modules/routes'
// import { useTabsStore } from '/@/store/modules/tabs'
// import { handleMatched, handleTabs } from '/@/utils/routes'

// 路由相关
const router = useRouter()
// const routesStore = useRoutesStore()
// const { getAllRoutes: allRoutes } = storeToRefs(routesStore)
// const tabsStore = useTabsStore()
// const { changeTabsMeta, addVisitedRoute } = tabsStore

// 定义数据和状态
const editRef = ref(null)
const tableRef = ref(null)
const stripe = ref(false)
const border = ref(false)
const lineHeight = ref('small')
// const fold = ref(true)
const list = ref([])
const isFullscreen = ref(false)
const listLoading = ref(true)
const total = ref(0)
const selectRows = ref([])
const columns = ref([
  // {
  //   label: '设备分类',
  //   prop: 'c_id',
  //   sortable: true,
  //   checked: true,
  // },
  {
    label: '场站',
    prop: 'tree_2_name',
    sortable: false,
    checked: true,
  },
  {
    label: '设备位置',
    prop: 'tree_3_name',
    sortable: false,
    checked: true,
  },
  {
    label: '锁具名称',
    prop: 'name',
    sortable: false,
    checked: true,
    minWidth: 180
  },
  {
    label: '编号',
    prop: 'addr',
    sortable: false,
    checked: true,
    minWidth: 125
  },
  {
    label: '设备ID',
    prop: 'entity_id',
    sortable: false,
    checked: true,
    minWidth: 160
  },
  // {
  //   label: '使用情况',
  //   prop: 'action_type',
  //   sortable: false,
  //   checked: true
  // },
  {
    label: '开关状态',
    prop: 'status',
    sortable: false,
    checked: true,
    minWidth: 75
  },
  {
    label: '操作次数',
    prop: 'action_num',
    sortable: true,
    checked: true,
    minWidth: 100
  },
  {
    label: '最近操作时间',
    prop: 'updated_at',
    sortable: 'custom',
    checked: true,
    minWidth: 150,
    // sortMethod(a, b) {
    //   const dateA = a.last_action_time ? new Date(a.last_action_time) : new Date(0);  // 如果为空则设为最早时间
    //   const dateB = b.last_action_time ? new Date(b.last_action_time) : new Date(0);
    //   return dateA - dateB;
    // }
  },
  {
    label: '最近操作人员',
    prop: 'last_action_username',
    sortable: false,
    checked: true,
    minWidth: 150
  },
  {
    label: '备注',
    prop: 'remark',
    sortable: false,
    checked: false,
    minWidth: 160
  },
  {
    label: '偏移量',
    prop: 'offset',
    sortable: true,
    checked: true,
  },
])
const checkList = ref([])
const queryForm = reactive({
  page: 1,
  page_size: 20,
  c_id: null,
  name: '',
  status: null,
  time_sort: null
})
// 树形数据示例
const treeData = ref([])
const filterText = ref('')
const treeRef = ref(null)

// 全屏处理
const { exit, enter, isFullscreen: _isFullscreen } = useFullscreen()

const finallyColumns = computed(() => columns.value.filter((item) => checkList.value.includes(item.label)))

// 树过滤
watch(filterText, (value) => {
  treeRef.value?.filter(value)
})

const filterNode = (value, data) => {
  if (!value) return true
  return data.label.includes(value)
}


const handleNodeClick = (value) => {
  if (value.parent_id === 0) {
    queryForm.c_id = null
  } else {
    queryForm.c_id = value.id
  }
  fetchData()
}


// 获取数据
const fetchData = async () => {
  listLoading.value = true
  const response = await getDeviceList(queryForm)
  list.value = response.data.list
  total.value = response.data.total
  listLoading.value = false
}

// 获取左侧树数据
const getTreeData = async () => {
  const { data } = await getAreaList()
  treeData.value = data
}

// 分页变化
const handleSizeChange = (value) => {
  queryForm.page = 1
  queryForm.page_size = value
  fetchData()
}

const handleCurrentChange = (value) => {
  queryForm.page = value
  fetchData()
}

const queryData = () => {
  queryForm.page = 1
  fetchData()
}

// 全屏切换
const clickFullScreen = () => {
  isFullscreen.value = !isFullscreen.value
  isFullscreen.value ? enter() : exit()
}

// 部门过滤
const departmentFilter = (id) => {
  const searchTree = (nodes, id) => {
    for (const node of nodes) {
      if (node.id === id) {
        return node.name; // 找到节点，返回 name
      }
      if (node.children) {
        const result = searchTree(node.children, id); // 递归查找子节点
        if (result) {
          return result; // 如果子节点找到，则返回结果
        }
      }
    }
    return null; // 未找到，返回 null
  };
  const result = searchTree(treeData.value, id);
  return result
}

// 折叠切换
// const handleFold = () => {
//   fold.value = !fold.value
// }

const setSelectRows = (value) => {
  selectRows.value = value
}

const handlMap = () => {
  router.push({
    path: '/device/maps',
  })
}

const handle3DMode = () => {
  router.push({
    path: '/device/3d',
  })
}

// 添加
const handleAdd = () => {
  // editRef.value.showEdit()
  $baseMessage('设备数据新增请先使用批量导入', 'warning', 'hey')
}

// 编辑
const handleEdit = (row = {}) => {
  editRef.value.showEdit(row)
}
// 上传模板
const handleUpload = () => {
  try {
    let input = document.createElement("input");
    input.style = "display: none";
    input.setAttribute("type", "file");
    input.setAttribute("accept", ".xls,.xlsx");  // 限制文件类型为 Excel
    document.body.appendChild(input);
    input.addEventListener("change", async (file) => {
      let File = file.target.files[0];
      if (!File) return;  // 如果没有文件，直接返回
        // 检查文件类型是否为 Excel 文件
      const validExtensions = ['.xls', '.xlsx'];
      const fileExtension = File.name.slice(File.name.lastIndexOf('.')).toLowerCase();

      if (!validExtensions.includes(fileExtension)) {
        $baseMessage('请选择Excel类型文件', 'warning', 'hey');
        return;
      }
      const formData = new FormData();
      formData.append("file", File);
      const response = await upLoadFile(formData)
      if (response.code === 200) {
        const res = await onImport({file: response.data})
        if (res.code === 200) {
          queryForm.page = 1
          queryForm.name = ''
          queryForm.c_id = null
          queryForm.status = null
          fetchData()
          $baseMessage('批量导入成功', 'success', 'hey')
        } else {
          $baseMessage('批量导入失败，请重试', 'error', 'hey')
        }
      } else {
        $baseMessage('上传文件失败，请重试', 'error', 'hey')
      }
    });
    input.click();
  } catch (error) {
    console.log(error);
  }
}

// 下载模板
const handleDownload = async() => {
  const response = await downLoadMb()
  if (response.code === 200) {
    window.open(response.data)
    $baseMessage('模板已下载，请在下载文件夹中查看', 'success', 'hey')
  } else {
    $baseMessage('模板下载失败，请重试！', 'warning', 'hey')
  }
}

const sortChange = ({ prop, order }) => {
  if (prop === 'updated_at') {
    if (order === 'ascending') {
      queryForm.time_sort = 0
    }
    if (order === 'descending') {
      queryForm.time_sort = 1
    }
    if (!order) {
      queryForm.time_sort = null
    }
    fetchData()
  }
}


// const handleDelete = (row) => {
//   if (row.id) {
//     $baseConfirm('您确定要删除当前项吗', null, async () => {
//       const response = await deleteDevice({ id: row.id })
//       $baseMessage(response.msg, 'success', 'hey')
//       await fetchData()
//     })
//   } else {
//     if (selectRows.value.length > 0) {
//       const id = selectRows.value.map((item) => item.id).join(',')
//       $baseConfirm('您确定要删除选中项吗', null, async () => {
//         const response = await deleteDevice({ id })
//         $baseMessage(response.msg, 'success', 'hey')
//         await fetchData()
//       })
//     } else {
//       $baseMessage('您未选中任何行', 'warning', 'hey')
//     }
//   }
// }

// 跳转详情
// const handleDetailStayTable = async () => {
//   if (selectRows.value.length > 0) {
//     for (let i = 0; i < selectRows.value.length; i++) {
//       const matched = handleMatched(allRoutes.value, '/vab/table/defaultTableDetail')
//       const tab = handleTabs({
//         ...matched.at(-1),
//         query: selectRows.value[i],
//       })
//       if (tab) {
//         await addVisitedRoute(tab)
//         await changeTabsMeta({
//           title: '详情页',
//           meta: {
//             title: `${tab.query.title} 详情页`,
//           },
//         })
//       }
//     }
//   } else {
//     $baseMessage('请至少选择一行进行详情页跳转', 'warning', 'hey')
//   }
// }

const handleDetail = (row) => {
  if (row.id) {
    router.push({
      path: '/device/devicesTableDetail',
      query: {
        ...row,
        timestamp: Date.now(),
      },
    })
  } else {
    if (selectRows.value.length === 1) {
      router.push({
        path: '/device/devicesTableDetail',
        query: {
          ...selectRows.value[0],
          timestamp: Date.now(),
        },
      })
    } else {
      $baseMessage('请选择一行进行详情页跳转', 'warning', 'hey')
    }
  }
}

// 监听全屏状态
watch(
  _isFullscreen,
  () => {
    isFullscreen.value = _isFullscreen.value
  },
  { immediate: true }
)

// 生命周期
onActivated(() => {
  tableRef.value?.doLayout()
})

onBeforeMount(() => {
  columns.value.forEach((item) => {
    if (item.checked) checkList.value.push(item.label)
  })
  fetchData()
  getTreeData()
})
</script>

<style lang="scss" scoped>
@import './index.scss'
</style>
