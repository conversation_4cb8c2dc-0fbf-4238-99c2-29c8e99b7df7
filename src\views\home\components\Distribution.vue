<template>
  <vab-card :body-style="{ height: '222px' }" skeleton>
    <template #header>
      <vab-icon icon="bar-chart-2-line" />
      操作时间分布图
      <!-- <el-tag class="card-header-tag" type="warning">周</el-tag> -->
    </template>
    <vab-chart :option="option" />
  </vab-card>
</template>

<script setup>
import { graphic } from 'echarts/core'
import { pull, sample } from 'lodash-es'
import { useSettingsStore } from '/@/store/modules/settings'
import { lightenColor } from '/@/utils/lightenColor'

defineOptions({
  name: 'Authorization',
})
const props = defineProps({
  datas: {
    type: Object,
    default: () => {},
  }
})
const settingsStore = useSettingsStore()
const theme = settingsStore.theme
let timer
const n = ref(5)
const option = reactive({
  tooltip: {
    trigger: 'axis',
    extraCssText: 'z-index:1',
  },
  grid: {
    top: '5%',
    left: '2%',
    right: '4%',
    bottom: '0%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: ['2时', '4时', '6时', '8时', '10时', '12时', '14时', '16时', '18时', '20时', '22时'],
    axisTick: {
      alignWithLabel: true,
    },
  },

  yAxis: {
    type: 'value',
  },

  series: {
    name: '开锁次数',
    type: 'bar',
    barWidth: '60%',
    data: Object.values(props.datas),
    itemStyle: {
      borderRadius: [2, 2, 0, 0],
      color: new graphic.LinearGradient(0, 0, 1, 0, [
        { offset: 0, color: lightenColor(theme.color, 50) },
        { offset: 1, color: theme.color },
      ]),
    },
  },
})

watch(
  () => theme.color,
  () => {
    option.series.itemStyle.color = new graphic.LinearGradient(0, 0, 1, 0, [
      { offset: 0, color: lightenColor(theme.color, 50) },
      { offset: 1, color: theme.color },
    ])
  },
  { immediate: true }
)

onMounted(() => {
  timer = setInterval(() => {
    if (n.value > 0) {
      n.value--
    } else {
      option.series.type = sample(pull(['bar', 'line', 'scatter'], option.series.type))
      n.value = 5
    }
  }, 1000)
})

onBeforeUnmount(() => {
  if (timer) clearInterval(timer)
})
</script>


<style lang="scss" scoped>
:deep() {
  .echarts {
    height: 180px !important;
  }
}

.bottom {
  padding-top: var(--el-padding);
  margin-top: 5px;
  text-align: left;
  border-top: 1px solid var(--el-border-color);
}
</style>
