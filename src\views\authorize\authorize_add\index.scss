
.step-form-container {
  .table-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: end;
    padding: 0 300px;
  }
  .success-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: end;
    // padding: 0 300px;
    .pay-top-content {
      text-align: center;

      .pay-success {
        display: block;
        margin: var(--el-margin) auto 5px auto;
        font-size: 40px;
        color: var(--el-color-success);
      }
    }
  }
  :deep() {
    .el-steps {
      margin: var(--el-margin) auto calc(var(--el-margin)) auto;

      .el-step__title.is-process {
        color: var(--el-color-primary);
      }

      .el-step__description.is-process {
        color: var(--el-color-primary);
      }

      .el-step__head {
        &.is-process {
          color: var(--el-color-primary);
          border-color: var(--el-color-primary);

          .el-step__icon.is-text {
            color: var(--el-color-primary);
            border: 1px solid;
          }

          .el-step__line {
            height: 1px;
          }
        }

        &.is-wait {
          .el-step__icon.is-text {
            border: 1px solid;
          }

          .el-step__line {
            height: 1px;
          }
        }

        &.is-finish {
          .el-step__icon.is-text {
            color: var(--el-color-white);
            background: var(--el-color-primary);
          }

          .el-step__line {
            height: 1px;
            background: var(--el-color-primary);
          }
        }
      }
    }
  }
}
