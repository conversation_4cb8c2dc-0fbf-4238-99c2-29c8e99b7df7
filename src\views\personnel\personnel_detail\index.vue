<template>
  <div class="device-table-detail-container">
    <el-page-header :content="'【' + route.query.username + '】详情页'" @back="goBack" />
    <el-row :gutter="20">
      <el-col :lg="14" :md="12" :sm="24" :xl="14" :xs="24">
        <vab-card class="auto-height-card">
          <div class="user-info">
            <ul class="user-info-list">
              <li>
                <el-descriptions border :column="2" title="人员详情">
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>用户名</template>
                    {{ detailData.username }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>手机号码</template>
                    {{ detailData.phone }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>所属部门</template>
                    {{ detailData.dept_name }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>角色</template>
                    {{ switchFilter(detailData.role_id) }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>人员类型</template>
                    {{ statusFilter(detailData.user_type_id) }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>操作次数</template>
                    {{ detailData.action_num }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>用户启用状态</template>
                    {{ detailData.is_del === 0 ? '正常' : '禁用'  }}
                  </el-descriptions-item>
                  <!-- <el-descriptions-item label-align="center" label-width="200">
                    <template #label>与上一次操作偏移</template>
                    {{ route.query.addr }}
                  </el-descriptions-item> -->
                  <!-- <el-descriptions-item label-align="center" label-width="200">
                    <template #label>最近一次操作人员</template>
                    {{ detailData.last_action_username }}
                  </el-descriptions-item> -->
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>最后一次操作时间</template>
                    {{detailData.last_action_time }}
                  </el-descriptions-item>
                  <!-- <el-descriptions-item label-align="center" label-width="200">
                    <template #label>备注</template>
                    {{ detailData.remark }}
                  </el-descriptions-item> -->
                </el-descriptions>
              </li>
              <!-- <li>
                <div class="btn-box">
                  <el-button native-type="submit" type="primary" @click="onSubmit">编辑</el-button>
                  <el-button native-type="submit" type="primary" @click="onSubmit">保存</el-button>
                </div>
              </li> -->
            </ul>
          </div>
        </vab-card>
      </el-col>
      <el-col :lg="10" :md="12" :sm="24" :xl="10" :xs="24">
        <vab-card class="auto-height-card">
          <el-tabs v-model="activeName">
            <el-tab-pane label="开/关锁记录" name="first">
              <vab-query-form>
                <vab-query-form-left-panel :span="24">
                    <el-form inline :model="unlockQueryForm" @submit.prevent>
                      <el-form-item label="开始时间">
                        <el-date-picker
                          v-model="unlockQueryForm.start_time"
                          format="YYYY/MM/DD"
                          placeholder="请选择开始时间"
                          type="date"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>
                      <el-form-item  label="结束时间">
                        <el-date-picker
                          v-model="unlockQueryForm.end_time"
                          format="YYYY/MM/DD"
                          placeholder="请选择结束时间"
                          type="date"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>
                      <el-form-item>
                        <el-button :icon="Search" type="primary" @click="queryUnlock">查询</el-button>
                      </el-form-item>
                    </el-form>
                </vab-query-form-left-panel>
              </vab-query-form>
              <el-timeline v-infinite-scroll="unlockLoad" :infinite-scroll-disabled="unlockDisabled" infinite-scroll-distance="10">
                <el-timeline-item v-for="(item, index) in unlockData" :key="index" :color="item.color" :timestamp="item.created_at">
                  <template #dot>
                    <vab-dot v-if="item.is_type === 1" type="error" />
                    <vab-dot v-if="item.is_type === 2" type="success" />
                  </template>
                  <span style="color: #409EFF;">{{ item.username }}({{ item.phone }})</span>对<span style="color: #409EFF;">{{ item.device_name }}</span>设备进行了
                  <span v-if="item.is_type === 1" style="color: #F56C6C;">关锁</span>
                  <span v-if="item.is_type === 2" style="color: #67C23A;">开锁</span>
                </el-timeline-item>
                <p v-if="unlockLoading">加载中...</p>
                <p v-if="unlockDisabled">没有数据了</p>
              </el-timeline>
            </el-tab-pane>
            <el-tab-pane label="授权记录" name="second">
              <vab-query-form>
                <vab-query-form-left-panel :span="24">
                    <el-form inline :model="authorizeQueryForm" @submit.prevent>
                      <el-form-item label="开始时间">
                        <el-date-picker
                          v-model="authorizeQueryForm.start_time"
                          format="YYYY/MM/DD"
                          placeholder="请选择开始时间"
                          type="date"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>
                      <el-form-item  label="结束时间">
                        <el-date-picker
                          v-model="authorizeQueryForm.end_time"
                          format="YYYY/MM/DD"
                          placeholder="请选择结束时间"
                          type="date"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>
                      <el-form-item>
                        <el-button :icon="Search" type="primary" @click="queryAuthorize">查询</el-button>
                      </el-form-item>
                    </el-form>
                </vab-query-form-left-panel>
              </vab-query-form>
              <el-timeline v-infinite-scroll="authorizeLoad" :infinite-scroll-disabled="authorizeDisabled">
                <el-timeline-item v-for="(item, index) in authorizeData" :key="index" :color="item.color" :timestamp="item.created_at">
                  <template #dot>
                    <vab-dot v-if="item.content.split('/')[2].includes('取消')" type="error" />
                    <vab-dot v-else type="success" />
                  </template>
                  <span style="color: #409EFF;">{{ item.content.split('/')[0].split('对')[0] }}</span>对<span style="color: #409EFF;">{{ item.content.split('/')[0].split('对')[1] }}</span>/<span style="color: #409EFF;">{{ item.content.split('/')[1] }}</span>/<span :style="item.content.split('/')[2].includes('取消') ? 'color: #F56C6C;' : 'color: #67C23A;'">{{ item.content.split('/')[2] }}</span>/<span style="color: #E6A23C;">{{ item.content.split('/')[3] }}</span>
                </el-timeline-item>
              <p v-if="authorizeLoading">加载中...</p>
              <p v-if="authorizeDisabled">没有数据了</p>
              </el-timeline>
            </el-tab-pane>
          </el-tabs>
        </vab-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { useTabsStore } from '/@/store/modules/tabs'
import { handleActivePath } from '/@/utils/routes'
import { getDetail } from '/@/api/personnel'
// import { getAreaList } from '/@/api/system'
import { getAuthorizeLog, getUnlockLog } from '/@/api/log'

// 定义组件名
defineOptions({
  name: 'DevicesTableDetail',
})

const route = useRoute()
const tabsStore = useTabsStore()
const { changeTabsMeta, delVisitedRoute } = tabsStore

const detailData = ref({})
// const treeData = ref([])

const unlockData = ref([])
const authorizeData = ref([])
// const activities = ref([
//   {
//     content: '<张三>对<xx>设备进行了<开锁>,设备偏移量<xx>米',
//     timestamp: '2021-04-05 20:46',
//     waver: 'success',
//   },
//   {
//     content: '<张三>对<李四>/<xx>设备进行了<授权>',
//     timestamp: '2021-04-05 20:46',
//     waver: 'success',
//   },
//   {
//     content: '<张三>对<李四>/<xx>设备进行了<取消授权>',
//     timestamp: '2021-04-05 20:46',
//     waver: 'error',
//   }
// ])

const unlockQueryForm = reactive({
  page: 1,
  page_size: 10,
  user_id: '',
  start_time: '',
  end_time: ''
})

const authorizeQueryForm = reactive({
  page: 1,
  page_size: 10,
  to_user_id: '',
  start_time: '',
  end_time: ''
})

const unlockLoading = ref(false)
const unlockDisabled = ref(true)

const authorizeLoading = ref(false)
const authorizeDisabled = ref(true)

// 初始化数据
const activeName = ref('first')

const goBack = async () => {
  await delVisitedRoute(handleActivePath(route, true))
  history.back()
}

const fetchData = async () => {
  const response = await getDetail({id: route.query.id})
  detailData.value = response.data
}

// 获取左侧树数据
// const getTreeData = async () => {
//   const { data } = await getAreaList()
//   treeData.value = data
// }

// 开关锁记录
const getUnlockData = async() => {
  unlockLoading.value = true
  unlockQueryForm.user_id = route.query.id
  const Unlock = await getUnlockLog(unlockQueryForm)
  if (Unlock.data.list.length < unlockQueryForm.page_size) {
    unlockDisabled.value = true
    unlockData.value = [...unlockData.value, ...Unlock.data.list]
  } else {
    unlockData.value = [...unlockData.value, ...Unlock.data.list]
    unlockQueryForm.page ++
    unlockDisabled.value = false
  }
  unlockLoading.value = false
}

// 授权记录
const getAuthorizeData = async() => {
  authorizeLoading.value = true
  authorizeQueryForm.to_user_id = route.query.id
  const Authorize = await getAuthorizeLog(authorizeQueryForm)
  if (Authorize.data.list.length <= authorizeQueryForm.page_size) {
    authorizeData.value = [...authorizeData.value, ...Authorize.data.list]
    authorizeDisabled.value = true
  } else {
    authorizeData.value = [...authorizeData.value, ...Authorize.data.list]
    authorizeQueryForm.page ++
    authorizeDisabled.value = false

  }
  authorizeLoading.value = false
}

// // 状态过滤(金温铁路)
// const statusFilter = (status) => {
//   switch (status) {
//   case 1: {
//     return '工程'
//   }
//   case 2: {
//     return '抢修'
//   }
//   case 3: {
//     return '巡检'
//   }
//   case 4: {
//     return '装维'
//   }
//   case 5: {
//     return '检查人员'
//   }
//   // No default
//   }
// }

// 状态过滤(铁塔)
const statusFilter = (status) => {
  switch (status) {
  case 1: {
    return '工程'
  }
  case 2: {
    return '抢修'
  }
  case 3: {
    return '巡检'
  }
  case 5: {
    return '检查人员'
  }
  // No default
  }
}

// 角色过滤
const switchFilter = (status) => {
  switch (status) {
  case 1: {
    return '管理员'
  }
  case 2: {
    return '用户'
  }
  // No default
  }
}

const unlockLoad = () => {
  getUnlockData()
}
const authorizeLoad = () => {
  getAuthorizeData()
}

// 时间查询开关锁记录
const queryUnlock = () => {
  unlockData.value = []
  unlockQueryForm.page = 1
  getUnlockData()
}

// 时间查询授权记录queryAuthorize
const queryAuthorize = () => {
  authorizeData.value = []
  authorizeQueryForm.page = 1
  getAuthorizeData()
}
onMounted(() => {
  changeTabsMeta({
    title: '详情页',
    meta: {
      title: `${route.query.username} 详情页`,
    },
  })
  fetchData()
  // getTreeData()
  getUnlockData()
  getAuthorizeData()
})
</script>


<style lang="scss" scoped>
@import './index.scss'
</style>
