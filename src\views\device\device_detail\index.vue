<template>
  <div class="device-table-detail-container">
    <el-page-header :content="'【' + route.query.name + '】详情页'" @back="goBack" />
    <el-row :gutter="20">
      <el-col :lg="14" :md="12" :sm="24" :xl="14" :xs="24">
        <vab-card class="auto-height-card">
          <div class="user-info">
            <ul class="user-info-list">
              <li>
                <el-descriptions border :column="2" title="设备详情">
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>设备ID</template>
                    {{ detailData.entity_id }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>设备名称</template>
                    {{ detailData.name }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>基地</template>
                    {{ detailData.tree_0_name }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>电站</template>
                    {{ detailData.tree_1_name }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>场站</template>
                    {{ detailData.tree_2_name }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>设备位置</template>
                    {{ detailData.tree_3_name }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>编号</template>
                    {{ detailData.addr }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>经纬度</template>
                    {{ detailData.lg }}, {{ detailData.lt }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>状态</template>
                    {{ statusFilter(detailData.action_type) }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>操作次数</template>
                    {{ detailData.action_num }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>开关状态</template>
                    {{ switchFilter(detailData.status) }}
                  </el-descriptions-item>
                  <!-- <el-descriptions-item label-align="center" label-width="200">
                    <template #label>与上一次操作偏移</template>
                    {{ route.query.addr }}
                  </el-descriptions-item> -->
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>最近一次操作人员</template>
                    {{ detailData.last_action_username }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>最后一次操作时间</template>
                    {{detailData.last_action_time }}
                  </el-descriptions-item>
                  <el-descriptions-item label-align="center" label-width="200">
                    <template #label>备注</template>
                    {{ detailData.remark }}
                  </el-descriptions-item>
                </el-descriptions>
              </li>
              <!-- <li>
                <div class="btn-box">
                  <el-button native-type="submit" type="primary" @click="onSubmit">编辑</el-button>
                  <el-button native-type="submit" type="primary" @click="onSubmit">保存</el-button>
                </div>
              </li> -->
            </ul>
          </div>
        </vab-card>
      </el-col>
      <el-col :lg="10" :md="12" :sm="24" :xl="10" :xs="24">
        <vab-card class="auto-height-card">
          <el-tabs v-model="activeName">
            <el-tab-pane label="开/关锁记录" name="first">
              <vab-query-form>
                <vab-query-form-left-panel :span="24">
                    <el-form inline :model="unlockQueryForm" @submit.prevent>
                      <el-form-item label="开始时间">
                        <el-date-picker
                          v-model="unlockQueryForm.start_time"
                          format="YYYY/MM/DD"
                          placeholder="请选择开始时间"
                          type="date"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>
                      <el-form-item  label="结束时间">
                        <el-date-picker
                          v-model="unlockQueryForm.end_time"
                          format="YYYY/MM/DD"
                          placeholder="请选择结束时间"
                          type="date"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>
                      <el-form-item>
                        <el-button :icon="Search" type="primary" @click="queryUnlock">查询</el-button>
                      </el-form-item>
                    </el-form>
                </vab-query-form-left-panel>
              </vab-query-form>
              <el-timeline v-infinite-scroll="unlockLoad" :infinite-scroll-disabled="unlockDisabled" infinite-scroll-distance="10">
                <el-timeline-item v-for="(item, index) in unlockData" :key="index" :color="item.color" :timestamp="item.created_at">
                  <template #dot>
                    <vab-dot v-if="item.is_type === 1" type="error" />
                    <vab-dot v-if="item.is_type === 2" type="success" />
                  </template>
                  <span style="color: #409EFF;">{{ item.username }}({{ item.phone }})</span>对<span style="color: #409EFF;">{{ item.device_name }}</span>设备进行了
                  <span v-if="item.is_type === 1" style="color: #F56C6C;">关锁</span>
                  <span v-if="item.is_type === 2" style="color: #67C23A;">开锁</span>
                  <el-button v-if="item.pic" size="small" style="margin-left: 14px;" type="primary" @click="onPreviewImage(item.id)">查看图片</el-button>
                  <img v-if="item.pic" v-show="false" :id="`viewer-image-${item.id}`" :src="`${baseUrl}${item.pic}`" />
                </el-timeline-item>
                <p v-if="unlockLoading">加载中...</p>
                <p v-if="unlockDisabled">没有数据了</p>
              </el-timeline>
            </el-tab-pane>
            <el-tab-pane label="授权记录" name="second">
              <vab-query-form>
                <vab-query-form-left-panel :span="24">
                    <el-form inline :model="authorizeQueryForm" @submit.prevent>
                      <el-form-item label="开始时间">
                        <el-date-picker
                          v-model="authorizeQueryForm.start_time"
                          format="YYYY/MM/DD"
                          placeholder="请选择开始时间"
                          type="date"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>
                      <el-form-item  label="结束时间">
                        <el-date-picker
                          v-model="authorizeQueryForm.end_time"
                          format="YYYY/MM/DD"
                          placeholder="请选择结束时间"
                          type="date"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>
                      <el-form-item>
                        <el-button :icon="Search" type="primary" @click="queryAuthorize">查询</el-button>
                      </el-form-item>
                    </el-form>
                </vab-query-form-left-panel>
              </vab-query-form>
              <el-timeline v-infinite-scroll="authorizeLoad" :infinite-scroll-disabled="authorizeDisabled">
                <el-timeline-item v-for="(item, index) in authorizeData" :key="index" :color="item.color" :timestamp="item.created_at">
                  <template #dot>
                    <vab-dot v-if="item.content.split('/')[2].includes('取消')" type="error" />
                    <vab-dot v-else type="success" />
                  </template>
                  <span style="color: #409EFF;">{{ item.content.split('/')[0].split('对')[0] }}</span>对<span style="color: #409EFF;">{{ item.content.split('/')[0].split('对')[1] }}</span>/<span style="color: #409EFF;">{{ item.content.split('/')[1] }}</span>/<span :style="item.content.split('/')[2].includes('取消') ? 'color: #F56C6C;' : 'color: #67C23A;'">{{ item.content.split('/')[2] }}</span>/<span style="color: #E6A23C;">{{ item.content.split('/')[3] }}</span>
                </el-timeline-item>
              <p v-if="authorizeLoading">加载中...</p>
              <p v-if="authorizeDisabled">没有数据了</p>
              </el-timeline>
            </el-tab-pane>
          </el-tabs>
        </vab-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { useTabsStore } from '/@/store/modules/tabs'
import { handleActivePath } from '/@/utils/routes'
import { getDetailByEntity } from '/@/api/device'
import { getAreaList } from '/@/api/system'
import { getAuthorizeLog, getUnlockLog } from '/@/api/log'
import Viewer from 'viewerjs'
import "viewerjs/dist/viewer.css"

// 定义组件名
defineOptions({
  name: 'DevicesTableDetail',
})

const baseUrl = import.meta.env.VITE_APP_BASE_URL
const route = useRoute()
const tabsStore = useTabsStore()
const { changeTabsMeta, delVisitedRoute } = tabsStore

const detailData = ref({})
const treeData = ref([])

const unlockData = ref([])
const authorizeData = ref([])
// const activities = ref([
//   {
//     content: '<张三>对<xx>设备进行了<开锁>,设备偏移量<xx>米',
//     timestamp: '2021-04-05 20:46',
//     waver: 'success',
//   },
//   {
//     content: '<张三>对<李四>/<xx>设备进行了<授权>',
//     timestamp: '2021-04-05 20:46',
//     waver: 'success',
//   },
//   {
//     content: '<张三>对<李四>/<xx>设备进行了<取消授权>',
//     timestamp: '2021-04-05 20:46',
//     waver: 'error',
//   }
// ])

const unlockQueryForm = reactive({
  page: 1,
  page_size: 10,
  device_id: '',
  start_time: '',
  end_time: ''
})

const authorizeQueryForm = reactive({
  page: 1,
  page_size: 10,
  device_id: '',
})

const unlockLoading = ref(false)
const unlockDisabled = ref(true)

const authorizeLoading = ref(false)
const authorizeDisabled = ref(true)

// 初始化数据
const activeName = ref('first')

const onPreviewImage = (id) => {
  const viewer = new Viewer(
    document.getElementById(`viewer-image-${id}`),
    {
      hidden: () => {
        viewer.destroy()
      }
    }
  );
  viewer.show()
}

const goBack = async () => {
  await delVisitedRoute(handleActivePath(route, true))
  history.back()
}

const fetchData = async () => {
  const response = await getDetailByEntity({entity_id: route.query.entity_id})
  detailData.value = response.data
}

// 获取左侧树数据
const getTreeData = async () => {
  const { data } = await getAreaList()
  treeData.value = data
}

// 开关锁记录
const getUnlockData = async() => {
  unlockLoading.value = true
  unlockQueryForm.device_id = route.query.id
  const Unlock = await getUnlockLog(unlockQueryForm)
  if (Unlock.data.list.length < unlockQueryForm.page_size) {
    unlockDisabled.value = true
    unlockData.value = [...unlockData.value, ...Unlock.data.list]
  } else {
    unlockData.value = [...unlockData.value, ...Unlock.data.list]
    unlockQueryForm.page ++
    unlockDisabled.value = false
  }
  unlockLoading.value = false
}

// 授权记录
const getAuthorizeData = async() => {
  authorizeLoading.value = true
  authorizeQueryForm.device_id = route.query.id
  const Authorize = await getAuthorizeLog(authorizeQueryForm)
  if (Authorize.data.list.length <= authorizeQueryForm.page_size) {
    authorizeData.value = [...authorizeData.value, ...Authorize.data.list]
    authorizeDisabled.value = true
  } else {
    authorizeData.value = [...authorizeData.value, ...Authorize.data.list]
    authorizeQueryForm.page ++
    authorizeDisabled.value = false

  }
  authorizeLoading.value = false
}

// 状态过滤
const statusFilter = (status) => {
  switch (status) {
  case 1: {
    return '未配置'
  }
  case 2: {
    return '配置失效'
  }
  case 3: {
    return '正常'
  }
  case 4: {
    return '故障'
  }
  // No default
  }
}

// 开关过滤
const switchFilter = (status) => {
  if (status === 1) {
    return '关'
  } else if (status === 2) {
    return '开'
  }
}

// 部门过滤
const departmentFilter = (id) => {
  const searchTree = (nodes, id) => {
    for (const node of nodes) {
      if (node.id === id) {
        return node.name
      }
      if (node.children) {
        const result = searchTree(node.children, id)
        if (result) {
          return result
        }
      }
    }
    return null
  };
  const result = searchTree(treeData.value, id);
  return result
}
const unlockLoad = () => {
  getUnlockData()
}
const authorizeLoad = () => {
  getAuthorizeData()
}

// 时间查询开关锁记录
const queryUnlock = () => {
  unlockData.value = []
  unlockQueryForm.page = 1
  getUnlockData()
}

// 时间查询授权记录queryAuthorize
const queryAuthorize = () => {
  authorizeData.value = []
  authorizeQueryForm.page = 1
  getAuthorizeData()
}
onMounted(() => {
  changeTabsMeta({
    title: '详情页',
    meta: {
      title: `${route.query.name} 详情页`,
    },
  })
  fetchData()
  getTreeData()
  getUnlockData()
  getAuthorizeData()
})
</script>


<style lang="scss" scoped>
@import './index.scss'
</style>
