<template>
  <div class="no-background-container">
    <div ref="canvasContainer" id="container">
      <canvas ref="babylonCanvas" id="babylonCanvas"></canvas>
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载模型中...</div>
      </div>
      <div v-if="error" class="error-overlay">
        <div class="error-text">{{ error }}</div>
        <button @click="retryLoad" class="retry-button">重试</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import {
  Engine,
  Scene,
  ArcRotateCamera,
  HemisphericLight,
  Vector3,
  Color3,
  DirectionalLight,
  ShadowGenerator
} from '@babylonjs/core'
// 动态导入 SceneLoader 以避免模块导入问题
import '@babylonjs/loaders/glTF'
import '@babylonjs/loaders/OBJ'
import '@babylonjs/loaders/STL'
// 确保 FBX 加载器被正确导入
import '@babylonjs/loaders'
// 注意：FBX 加载器在 @babylonjs/loaders 中
import Stage from "/@/assets/model_3d/stage.fbx"

// 响应式变量
const canvasContainer = ref(null)
const babylonCanvas = ref(null)
const loading = ref(true)
const error = ref('')

// Babylon.js 相关变量
let engine = null
let scene = null
let camera = null

// 初始化 Babylon.js 场景
const initBabylon = () => {
  try {
    // 创建引擎
    engine = new Engine(babylonCanvas.value, true, {
      preserveDrawingBuffer: true,
      stencil: true,
      antialias: true
    })

    // 创建场景
    scene = new Scene(engine)
    scene.clearColor = new Color3(0.2, 0.2, 0.3)

    // 创建相机
    camera = new ArcRotateCamera(
      "camera",
      -Math.PI / 2,
      Math.PI / 2.5,
      10,
      Vector3.Zero(),
      scene
    )
    camera.attachControls(babylonCanvas.value, true)
    camera.setTarget(Vector3.Zero())

    // 设置相机限制
    camera.lowerBetaLimit = 0.1
    camera.upperBetaLimit = Math.PI / 2
    camera.lowerRadiusLimit = 2
    camera.upperRadiusLimit = 50

    // 创建光源
    const hemisphericLight = new HemisphericLight("hemisphericLight", new Vector3(0, 1, 0), scene)
    hemisphericLight.intensity = 0.7

    // 创建方向光
    const directionalLight = new DirectionalLight("directionalLight", new Vector3(-1, -1, -1), scene)
    directionalLight.intensity = 0.5
    directionalLight.position = new Vector3(20, 40, 20)

    // 创建阴影生成器
    const shadowGenerator = new ShadowGenerator(1024, directionalLight)
    shadowGenerator.useBlurExponentialShadowMap = true
    shadowGenerator.blurKernel = 32

    // 加载 FBX 模型
    loadModel()

    // 启动渲染循环
    engine.runRenderLoop(() => {
      scene.render()
    })

    // 处理窗口大小变化
    window.addEventListener('resize', handleResize)

  } catch (error_) {
    console.error('初始化 Babylon.js 失败:', error_)
    error.value = '初始化 3D 引擎失败'
    loading.value = false
  }
}

// 加载 FBX 模型
const loadModel = async () => {
  try {
    loading.value = true
    error.value = ''

    console.log('开始加载模型:', Stage)

    // 动态导入 SceneLoader 以避免模块导入问题
    const { SceneLoader } = await import('@babylonjs/core')

    // 使用新的 API 加载 FBX 模型
    const result = await SceneLoader.ImportMeshAsync(null, "", Stage, scene)

    if (result.meshes && result.meshes.length > 0) {
      console.log('模型加载成功，网格数量:', result.meshes.length)

      // 设置模型属性
      result.meshes.forEach((mesh, index) => {
        if (mesh.name !== '__root__') {
          console.log(`网格 ${index}: ${mesh.name}`)

          // 启用阴影
          mesh.receiveShadows = true
          if (mesh.material) {
            // 可以在这里调整材质属性
          }
        }
      })

      // 自动调整相机位置以适应模型
      if (result.meshes.length > 1) {
        const boundingInfo = scene.getBoundingBoxRenderer().frontBoundingBoxes[0]
        if (boundingInfo) {
          camera.setTarget(boundingInfo.centerWorld)
          const radius = boundingInfo.diagonalLength
          camera.radius = radius * 1.5
        }
      }

      loading.value = false
    } else {
      throw new Error('模型加载失败：没有找到有效的网格')
    }

  } catch (error_) {
    console.error('加载模型失败:', error_)
    error.value = `加载模型失败: ${error_.message}`
    loading.value = false
  }
}

// 重试加载
const retryLoad = () => {
  loadModel()
}

// 处理窗口大小变化
const handleResize = () => {
  if (engine) {
    engine.resize()
  }
}

// 组件挂载时初始化
onMounted(() => {
  if (babylonCanvas.value) {
    initBabylon()
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)

  if (engine) {
    engine.dispose()
  }

  if (scene) {
    scene.dispose()
  }
})
</script>

<style scoped>
.no-background-container {
  position: relative;
  width: 100%;
  height: 100%;
}

#container {
  width: 100%;
  height: 75vh;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

#babylonCanvas {
  width: 100%;
  height: 100%;
  display: block;
  outline: none;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(220, 53, 69, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  z-index: 10;
}

.error-text {
  font-size: 16px;
  margin-bottom: 16px;
  text-align: center;
  padding: 0 20px;
}

.retry-button {
  padding: 8px 16px;
  background: white;
  color: #dc3545;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: #f8f9fa;
  transform: translateY(-1px);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
