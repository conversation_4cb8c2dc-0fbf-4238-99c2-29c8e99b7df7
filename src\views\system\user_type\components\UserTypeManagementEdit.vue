<template>
  <vab-dialog v-model="dialogFormVisible" append-to-body :title="title" width="500px" @close="close">
    <el-form ref="formRef" label-width="80px" :model="form" :rules="rules">
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" clearable />
      </el-form-item>
      <!-- <el-form-item label="排序" prop="order">
        <el-input v-model="form.order" clearable />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </vab-dialog>
</template>

<script setup>
import { nextTick, onBeforeMount, reactive, ref } from 'vue';
import { createUserType, updateUserType } from '/@/api/system';

defineOptions({
  name: 'UserTypeManagementEdit',
});

const emit = defineEmits(['fetch-data']);

const formRef = ref(null);
const form = reactive({});
const rules = reactive({
  name: [{ required: true, trigger: 'blur', message: '请输入名称' }],
  // order: [{ required: true, trigger: 'blur', message: '请输入排序' }],
});
const title = ref('');
const dialogFormVisible = ref(false);

const fetchData = async () => {
  // pass
};

const showEdit = (row) => {
  dialogFormVisible.value = true;
  nextTick(() => {
    if (row) {
      delete row.value; // 删除 `value` 属性，避免冲突
      title.value = '编辑';
      Object.assign(form, row);
    } else {
      title.value = '添加';
      for (let key in form) {
        delete form[key]
      }
      Object.assign(form, {})
    }
  });
};

defineExpose({
  showEdit,
});

const close = () => {
  console.log(formRef.value);
  if (formRef.value) {
    formRef.value.clearValidate();
    formRef.value.resetFields();
  }
  fetchData()
  emit('fetch-data');
};

const save = () => {
  if (title.value === '编辑' && formRef.value) {
    formRef.value.validate(async (valid) => {
      if (valid) {
        await updateUserType(form);
        $baseMessage('编辑成功', 'success', 'hey');
        close();
        dialogFormVisible.value = false;
      }
    });
  } else if (title.value === '添加' && formRef.value) {
    formRef.value.validate(async (valid) => {
      if (valid) {
        await createUserType(form);
        $baseMessage('添加成功', 'success', 'hey');
        close();
        dialogFormVisible.value = false;
      }
    });
  }
};

onBeforeMount(() => {
  fetchData()
});
</script>


<style lang="scss" scoped>
:deep() {
  .el-select {
    width: 100%;
  }
}
</style>
