<template>
  <vab-dialog v-model="dialogFormVisible" append-to-body :title="title" width="500px" @close="close">
    <el-form ref="formRef" :model="form" :rules="rules">
      <el-form-item label="标题" prop="sys_des">
        <el-input v-model.trim="form.sys_des" clearable />
      </el-form-item>
      <el-form-item :label="label" prop="fz">
        <el-input v-model.trim="form.fz" clearable />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </vab-dialog>
</template>

<script setup>
import { updateAlarm } from '/@/api/system'

defineOptions({
  name: 'DefaultTableEdit',
})

const emit = defineEmits(['fetch-data'])

const formRef = ref()
const title = ref('')
const label = ref('')
const dialogFormVisible = ref(false)
const form = reactive({
  sys_des: '',
  fz: '',
})
const sys_des = ref('')
const fz = ref('')
const rules = reactive({
  sys_des: [{ required: true, trigger: 'blur', message: '请输入标题' }],
  fz: [{ required: true, trigger: 'blur', message: '请输入阈值' }],
})

const showEdit = (row) => {
  dialogFormVisible.value = true
  nextTick(() => {
    if (row) {
      switch (row.sys_type) {
      case '1': {
        label.value = '阈值(单位:小时)'

      break;
      }
      case '2': {
        label.value = '阈值(单位:天)'

      break;
      }
      case '3': {
        label.value = '阈值(单位:米)'

      break;
      }
      // No default
      }
      title.value = '编辑'
      sys_des.value = row.sys_des
      fz.value = row.fz
      Object.assign(form, row)
    }
  })
}

defineExpose({
  showEdit,
})

const close = () => {
  formRef.value?.clearValidate()
  formRef.value?.resetFields()
  emit('fetch-data')
}

const save = async() => {
  if (sys_des.value === form.sys_des && fz.value === form.fz) {
    await $baseMessage('没有进行修改', 'warning', 'hey')
    close()
    dialogFormVisible.value = false
    return
  }
  formRef.value?.validate(async (valid) => {
    if (valid) {
      await updateAlarm(form)
      await $baseMessage('修改成功', 'success', 'hey')
      await close()
      dialogFormVisible.value = false
    }
  })
}
</script>
