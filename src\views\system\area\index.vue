<template>
  <div class="department-management-container auto-height-container">
    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-button :icon="Plus" type="primary" @click="handleAdd">添加</el-button>
        <!-- <el-button :icon="Delete" type="danger" @click="handleDelete">批量删除</el-button> -->
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-form inline :model="queryForm" @submit.prevent>
          <el-form-item>
            <el-input v-model.trim="queryForm.label" clearable placeholder="请输入名称" />
          </el-form-item>
          <el-form-item>
            <el-button :icon="Search" :loading="listLoading" type="primary" @click="queryData">查询</el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableRef"
      v-loading="listLoading"
      border
      :data="list"
      default-expand-all
      row-key="id"
      :tree-props="{ children: 'children' }"
      @selection-change="setSelectRows"
    >
      <el-table-column type="selection" width="38" />
      <el-table-column align="center" label="名称" min-width="120" prop="name" />
      <!-- <el-table-column align="center" label="父节点Value" min-width="120" prop="parentValue" /> -->
      <el-table-column align="center" label="ID" prop="id" />
      <el-table-column align="center" label="创建时间" min-width="160" prop="created_at" show-overflow-tooltip />
      <el-table-column align="center" label="操作" width="150">
        <template #default="{ row }">
          <el-button text type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button :disabled="!row.parent_id" text type="primary" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty class="vab-data-empty" description="暂无数据" />
      </template>
    </el-table>
    <area-management-edit ref="editRef" @fetch-data="fetchData" />
  </div>
</template>

<script setup>
import { onActivated, onBeforeMount, reactive, ref } from 'vue';
import { Plus, Search } from '@element-plus/icons-vue';
import { deleteArea, getAreaList } from '/@/api/system';
import AreaManagementEdit from '/@/views/system/area/components/AreaManagementEdit.vue';

defineOptions({
  label: 'DepartmentManagement',
});

const tableRef = ref(null);
const editRef = ref(null);
const list = ref([]);
const listLoading = ref(true);

const selectRows = ref([]);
const queryForm = reactive({
  pageNo: 1,
  pageSize: 20,
  label: '',
});

const setSelectRows = (value) => {
  selectRows.value = value;
};

const handleAdd = () => {
  if (editRef.value) {
    editRef.value.showEdit();
  }
};

const handleEdit = (row = {}) => {
  if (editRef.value) {
    editRef.value.showEdit(row);
  }
};

const handleDelete = (row = {}) => {
  if (row.id) {
    $baseConfirm('您确定要删除当前项吗', null, async () => {
      await deleteArea({ id: row.id });
      $baseMessage('删除成功', 'success', 'hey');
      await fetchData();
    });
  } else {
    if (selectRows.value.length > 0) {
      const id = selectRows.value.map((item) => item.id).join(',');
      $baseConfirm('您确定要删除选中项吗', null, async () => {
        await deleteArea({ id });
        $baseMessage('删除成功', 'success', 'hey');
        await fetchData();
      });
    } else {
      $baseMessage('您未选中任何行', 'warning', 'hey');
    }
  }
};

const queryData = () => {
  queryForm.pageNo = 1;
  fetchData();
};

const fetchData = async () => {
  listLoading.value = true;
  const { data } = await getAreaList();
  list.value = data;
  // total.value = data.total;
  listLoading.value = false;
};

onActivated(() => {
  if (tableRef.value) {
    tableRef.value.doLayout();
  }
});

onBeforeMount(() => {
  fetchData();
});
</script>
