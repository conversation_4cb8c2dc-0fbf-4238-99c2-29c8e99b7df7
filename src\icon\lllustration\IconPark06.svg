<svg viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#a)"><g filter="url(#b)"><rect x="207.225" y="191.003" width="47.777" height="87.94" rx="23.888" transform="rotate(-45 207.225 191.003)" fill="#2F9BFF"/></g><circle cx="174.365" cy="124.365" r="94.535" transform="rotate(-45 174.365 124.365)" stroke="#2F9BFF" stroke-width="13.191"/><circle opacity=".4" cx="174.359" cy="124.365" r="48.367" transform="rotate(-45 174.359 124.365)" fill="#2F9BFF"/><g filter="url(#c)"><circle cx="174.365" cy="124.365" r="87.94" transform="rotate(-45 174.365 124.365)" fill="#B7D5FF" fill-opacity=".3"/></g><g filter="url(#d)"><path d="M156.423 102.093c-.303-.909-1.212-1.515-2.122-1.515h-6.668c-.909 0-1.818.606-2.121 1.515l-17.579 46.07c-.303.606-.303 1.515.303 2.121.303.606 1.212.909 1.818.909h6.365c.909 0 1.819-.606 2.122-1.515l4.546-12.427h16.064L164 149.678c.303.909 1.212 1.515 2.122 1.515h6.971c.909 0 1.515-.303 1.818-.909.303-.606.606-1.515.303-2.121l-18.791-46.07Zm-10.002 25.763 3.94-10.608c.303-.606.303-1.213.606-1.819.303.909.607 1.516.91 2.425l3.637 9.699h-9.093v.303Zm63.952-22.732c-2.122-1.818-4.85-3.031-7.578-3.94-2.121-.606-4.849-.606-8.789-.606h-15.761c-1.212 0-2.424.909-2.424 2.425v46.069c0 1.212.909 2.425 2.424 2.425h16.67c3.031 0 5.759-.304 7.88-.91 2.425-.606 4.547-1.515 6.365-2.727 1.819-1.213 3.334-2.728 4.85-4.85 1.515-2.121 2.727-4.546 3.334-7.274.909-3.031 1.212-6.365 1.212-10.002 0-4.243-.606-8.183-1.819-11.82-1.515-3.637-3.637-6.365-6.364-8.79Zm-3.941 28.793c-.606 2.122-1.515 3.638-2.727 4.85-.91.909-1.819 1.515-3.334 1.818-1.516.607-3.637.607-6.365.607h-7.577V110.58h7.274c4.243 0 6.061.303 6.971.909 1.818.606 3.334 2.122 4.546 4.243 1.212 2.122 2.122 5.456 2.122 10.002.303 3.031 0 5.759-.91 8.183Z" fill="#fff"/></g></g><defs><filter id="b" x="217.119" y="167.115" width="76.176" height="77.147" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy=".971"/><feGaussianBlur stdDeviation=".971"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.21 0"/><feBlend in2="shape" result="effect1_innerShadow_35_2166"/></filter><filter id="c" x="60.415" y="10.415" width="227.901" height="227.902" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feGaussianBlur in="BackgroundImage" stdDeviation="13.005"/><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_35_2166"/><feBlend in="SourceGraphic" in2="effect1_backgroundBlur_35_2166" result="shape"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dx="4.162" dy="10.404"/><feGaussianBlur stdDeviation="6.863"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/><feBlend in2="shape" result="effect2_innerShadow_35_2166"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="2.601"/><feGaussianBlur stdDeviation="2.601"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"/><feBlend in2="effect2_innerShadow_35_2166" result="effect3_innerShadow_35_2166"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="-2.601"/><feGaussianBlur stdDeviation="2.601"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix values="0 0 0 0 0.103594 0 0 0 0 0.52793 0 0 0 0 0.920833 0 0 0 0.16 0"/><feBlend in2="effect3_innerShadow_35_2166" result="effect4_innerShadow_35_2166"/></filter><filter id="d" x="127.734" y="100.578" width="90.822" height="56.471" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="5.552"/><feColorMatrix values="0 0 0 0 0.184314 0 0 0 0 0.607843 0 0 0 0 1 0 0 0 0.3 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow_35_2166"/><feBlend in="SourceGraphic" in2="effect1_dropShadow_35_2166" result="shape"/></filter><clipPath id="a"><path fill="#fff" d="M0 0h400v300H0z"/></clipPath></defs></svg>