import request from '/@/utils/request'

export function getPersonnelList(params: any) {
  return request({
    url: '/user/index',
    method: 'get',
    params,
  })
}

export function createPersonnel(data: any) {
  return request({
    url: '/user/create',
    method: 'post',
    data,
  })
}

export function getDetail(params: any) {
  return request({
    url: '/user/show',
    method: 'get',
    params,
  })
}

export const updatePersonnel = (data: any) => {
  return request({
    url: '/user/update',
    method: 'post',
    data,
  })
}

export const deletePersonnel = (data: any) => {
  return request({
    url: '/user/delete',
    method: 'post',
    data,
  })
}
