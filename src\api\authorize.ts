import request from '/@/utils/request'

export const getList = (params: any) => {
  return request({
    url: '/approval/list',
    method: 'get',
    params,
  })
}

export const toExamine = (data: any) => {
  return request({
    url: '/approval/isPass',
    method: 'post',
    data,
  })
}

export const onAuthorize = (data: any) => {
  return request({
    url: '/device/fenpei',
    method: 'post',
    data,
  })
}

export const revokeAuthorize = (data: any) => {
  return request({
    url: '/device/cancelFenpei',
    method: 'post',
    data,
  })
}

export const getAllPassStatus = (params: any) => {
  return request({
    url: '/DeviceAuthLog/getAllPassStatus',
    method: 'get',
    params,
  })
}

export const setAllPassDevice = (data: any) => {
  return request({
    url: '/DeviceAuthLog/setAllPassDevice',
    method: 'post',
    data,
  })
}

export const cancelAllPassDevice = (data: any) => {
  return request({
    url: '/DeviceAuthLog/cancelAllPassDevice',
    method: 'post',
    data,
  })
}
